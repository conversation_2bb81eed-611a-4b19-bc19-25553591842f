const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');
const logger = require('./logger');

// Criar diretórios de upload se não existirem
const createUploadDirs = () => {
  const dirs = [
    'backend/uploads',
    'backend/uploads/avatars',
    'backend/uploads/documents',
    'backend/uploads/temp'
  ];
  
  dirs.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
      logger.info(`Diretório criado: ${dir}`);
    }
  });
};

// Inicializar diretórios
createUploadDirs();

// Configuração de storage para avatars
const avatarStorage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, 'backend/uploads/avatars/');
  },
  filename: (req, file, cb) => {
    // Gerar nome único: userId_timestamp.extensão
    const userId = req.user.id;
    const timestamp = Date.now();
    const extension = path.extname(file.originalname);
    const filename = `${userId}_${timestamp}${extension}`;
    cb(null, filename);
  }
});

// Configuração de storage para documentos
const documentStorage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, 'backend/uploads/documents/');
  },
  filename: (req, file, cb) => {
    // Gerar nome único: uuid_originalname
    const uniqueId = uuidv4();
    const extension = path.extname(file.originalname);
    const baseName = path.basename(file.originalname, extension);
    const filename = `${uniqueId}_${baseName}${extension}`;
    cb(null, filename);
  }
});

// Filtro de arquivos para avatars (apenas imagens)
const avatarFileFilter = (req, file, cb) => {
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
  
  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('Tipo de arquivo não permitido. Apenas JPEG, PNG e GIF são aceitos.'), false);
  }
};

// Filtro de arquivos para documentos
const documentFileFilter = (req, file, cb) => {
  const allowedTypes = process.env.UPLOAD_ALLOWED_TYPES?.split(',') || 
    ['image/jpeg', 'image/png', 'application/pdf'];
  
  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error(`Tipo de arquivo não permitido. Tipos aceitos: ${allowedTypes.join(', ')}`), false);
  }
};

// Configuração do multer para avatars
const avatarUpload = multer({
  storage: avatarStorage,
  fileFilter: avatarFileFilter,
  limits: {
    fileSize: parseInt(process.env.UPLOAD_MAX_SIZE) || 5 * 1024 * 1024, // 5MB
    files: 1 // Apenas um arquivo por vez
  }
});

// Configuração do multer para documentos
const documentUpload = multer({
  storage: documentStorage,
  fileFilter: documentFileFilter,
  limits: {
    fileSize: parseInt(process.env.UPLOAD_MAX_SIZE) || 5 * 1024 * 1024, // 5MB
    files: 5 // Até 5 arquivos por vez
  }
});

// Função para deletar arquivo antigo
const deleteOldFile = (filePath) => {
  if (filePath && fs.existsSync(filePath)) {
    try {
      fs.unlinkSync(filePath);
      logger.info(`Arquivo antigo deletado: ${filePath}`);
    } catch (error) {
      logger.error(`Erro ao deletar arquivo: ${filePath}`, error);
    }
  }
};

// Função para gerar URL do avatar
const getAvatarUrl = (filename) => {
  if (!filename) return null;
  return `/uploads/avatars/${filename}`;
};

// Função para gerar URL do documento
const getDocumentUrl = (filename) => {
  if (!filename) return null;
  return `/uploads/documents/${filename}`;
};

module.exports = {
  avatarUpload,
  documentUpload,
  deleteOldFile,
  getAvatarUrl,
  getDocumentUrl,
  createUploadDirs
};
