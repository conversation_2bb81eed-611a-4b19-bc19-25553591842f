
import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Building2, AlertTriangle } from 'lucide-react';

const TransferenciaSTC = () => {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Transferências STC</h1>
          <p className="text-gray-600">Sistema de Transferências e Compensações</p>
        </div>
      </div>

      <Card className="border-yellow-200 bg-yellow-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-yellow-700">
            <AlertTriangle className="h-5 w-5" />
            Funcionalidade em Desenvolvimento
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center gap-3">
              <Building2 className="h-8 w-8 text-gray-400" />
              <div>
                <h3 className="font-semibold text-gray-700">Sistema STC</h3>
                <p className="text-gray-600">
                  O módulo de transferências STC está atualmente em desenvolvimento e será disponibilizado em breve.
                </p>
              </div>
            </div>
            
            <div className="bg-white p-4 rounded-lg border">
              <h4 className="font-medium text-gray-800 mb-2">Funcionalidades Previstas:</h4>
              <ul className="space-y-1 text-sm text-gray-600">
                <li>• Transferências para contas de outros bancos</li>
                <li>• Sistema de compensação automática</li>
                <li>• Processamento em lotes</li>
                <li>• Conciliação bancária</li>
                <li>• Relatórios de transferências STC</li>
              </ul>
            </div>

            <Button disabled className="w-full" size="lg">
              Funcionalidade Indisponível
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default TransferenciaSTC;
