{"name": "twins-bank-backend", "version": "1.0.0", "description": "Backend API para o sistema bancário Twins_Bank", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "migrate": "node src/database/migrate.js", "seed": "node src/database/seed.js", "lint": "eslint src/", "lint:fix": "eslint src/ --fix"}, "keywords": ["banking", "api", "nodejs", "express", "mysql", "jwt"], "author": "Twins_Bank Development Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "mysql2": "^3.6.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "joi": "^17.9.2", "cors": "^2.8.5", "helmet": "^7.0.0", "dotenv": "^16.3.1", "express-rate-limit": "^6.8.1", "uuid": "^9.0.0", "multer": "^1.4.5-lts.1", "moment": "^2.29.4", "winston": "^3.10.0"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.2", "supertest": "^6.3.3", "eslint": "^8.45.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.1.1"}, "engines": {"node": ">=18.0.0"}}