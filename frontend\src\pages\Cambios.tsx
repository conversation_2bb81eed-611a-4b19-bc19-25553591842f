
import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { DollarSign, TrendingUp, TrendingDown, RefreshCw } from 'lucide-react';

const Cambios = () => {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Operações de Câmbio</h1>
          <p className="text-gray-600 dark:text-gray-400">Gestão de moedas estrangeiras e taxas de câmbio</p>
        </div>
        <Button className="flex items-center gap-2">
          <DollarSign className="h-4 w-4" />
          Nova Operação
        </Button>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">USD/KZ</CardTitle>
            <TrendingUp className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">760.12</div>
            <p className="text-xs text-green-600">+2.07 (+0.27%)</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">GBP/KZ</CardTitle>
            <TrendingDown className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">1011.06</div>
            <p className="text-xs text-red-600">-4.05 (-0.40%)</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">CHF/KZ</CardTitle>
            <TrendingUp className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">888.84</div>
            <p className="text-xs text-green-600">+1.08 (+0.12%)</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Volume Hoje</CardTitle>
            <DollarSign className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">40.680K Kz</div>
            <p className="text-xs text-muted-foreground">12 operações</p>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-6 lg:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              Conversor de Moedas
              <Button variant="outline" size="sm">
                <RefreshCw className="h-4 w-4" />
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium dark:text-gray-100">De</label>
                <select className="w-full p-2 border border-gray-300 rounded-md dark:bg-gray-700 dark:text-gray-100 dark:border-gray-600">
                  <option value="KZ">KZ - Kwanza</option>
                  <option value="USD">USD - Dólar Americano</option>
                  <option value="GBP">GBP - Libra Esterlina</option>
                  <option value="CHF">CHF - Franco Suíço</option>
                </select>
              </div>
              <div>
                <label className="text-sm font-medium dark:text-gray-100">Para</label>
                <select className="w-full p-2 border border-gray-300 rounded-md dark:bg-gray-700 dark:text-gray-100 dark:border-gray-600">
                  <option value="USD">USD - Dólar Americano</option>
                  <option value="KZ">KZ - Kwanza</option>
                  <option value="GBP">GBP - Libra Esterlina</option>
                  <option value="CHF">CHF - Franco Suíço</option>
                </select>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium dark:text-gray-100">Valor</label>
                <Input type="number" placeholder="0.00" className="dark:bg-gray-700 dark:text-gray-100" />
              </div>
              <div>
                <label className="text-sm font-medium dark:text-gray-100">Resultado</label>
                <Input value="0.00" readOnly className="bg-gray-50 dark:bg-gray-700 dark:text-gray-100" />
              </div>
            </div>

            <div className="bg-blue-50 p-3 rounded-lg">
              <div className="text-sm text-blue-700">
                <strong>Taxa aplicada:</strong> 1 KZ = 0.0013 USD
              </div>
              <div className="text-xs text-blue-600 mt-1">
                Atualizado há 2 minutos
              </div>
            </div>

            <Button className="w-full">Executar Operação</Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Últimas Operações</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div>
                  <div className="font-medium dark:text-gray-100">KZ → USD</div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">Cliente: João Silva</div>
                </div>
                <div className="text-right">
                  <div className="font-bold dark:text-gray-100">900.000 Kz → $ 1.185</div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">14:32</div>
                </div>
              </div>

              <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div>
                  <div className="font-medium dark:text-gray-100">GBP → KZ</div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">Cliente: Maria Santos</div>
                </div>
                <div className="text-right">
                  <div className="font-bold dark:text-gray-100">£ 500 → 505.530 Kz</div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">13:15</div>
                </div>
              </div>

              <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div>
                  <div className="font-medium dark:text-gray-100">USD → KZ</div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">Cliente: Ana Costa</div>
                </div>
                <div className="text-right">
                  <div className="font-bold dark:text-gray-100">$ 2.000 → 1.520.240 Kz</div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">12:45</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Cambios;
