
import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Banknote, MapPin, Wifi, Battery, AlertTriangle, CheckCircle } from 'lucide-react';

const ATM = () => {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Gestão de ATM</h1>
          <p className="text-gray-600 dark:text-gray-400">Monitorização e controlo dos caixas automáticos</p>
        </div>
        <Button className="flex items-center gap-2">
          <Banknote className="h-4 w-4" />
          Relatório Geral
        </Button>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">ATMs Online</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">2</div>
            <p className="text-xs text-muted-foreground">de 3 total</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">ATMs Offline</CardTitle>
            <AlertTriangle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">1</div>
            <p className="text-xs text-muted-foreground">Necessita manutenção</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total em Caixas</CardTitle>
            <Banknote className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">48.312.000 Kz</div>
            <p className="text-xs text-muted-foreground">Distribuído por 3 ATMs</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Transações Hoje</CardTitle>
            <Banknote className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">127</div>
            <p className="text-xs text-muted-foreground">13.905.000 Kz levantado</p>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-6 lg:grid-cols-3">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MapPin className="h-5 w-5" />
              ATM Principal
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span>Status:</span>
                <div className="flex items-center gap-1 text-green-600">
                  <CheckCircle className="h-4 w-4" />
                  <span className="font-bold">Online</span>
                </div>
              </div>
              <div className="flex justify-between">
                <span>Saldo:</span>
                <span className="font-bold">40.707.000 Kz</span>
              </div>
              <div className="flex justify-between">
                <span>Última transação:</span>
                <span className="text-sm">14:32</span>
              </div>
              <div className="flex justify-between items-center">
                <span>Conexão:</span>
                <Wifi className="h-4 w-4 text-green-600" />
              </div>
              <div className="flex justify-between items-center">
                <span>Energia:</span>
                <Battery className="h-4 w-4 text-green-600" />
              </div>
              <div className="bg-green-50 p-2 rounded text-center text-sm text-green-700">
                Operacional
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MapPin className="h-5 w-5" />
              ATM Entrada
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span>Status:</span>
                <div className="flex items-center gap-1 text-yellow-600">
                  <AlertTriangle className="h-4 w-4" />
                  <span className="font-bold">Baixo Saldo</span>
                </div>
              </div>
              <div className="flex justify-between">
                <span>Saldo:</span>
                <span className="font-bold text-orange-600">7.605.000 Kz</span>
              </div>
              <div className="flex justify-between">
                <span>Última transação:</span>
                <span className="text-sm">14:15</span>
              </div>
              <div className="flex justify-between items-center">
                <span>Conexão:</span>
                <Wifi className="h-4 w-4 text-green-600" />
              </div>
              <div className="flex justify-between items-center">
                <span>Energia:</span>
                <Battery className="h-4 w-4 text-green-600" />
              </div>
              <div className="bg-yellow-50 p-2 rounded text-center text-sm text-yellow-700">
                Necessita Carregamento
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MapPin className="h-5 w-5" />
              ATM Drive-Through
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span>Status:</span>
                <div className="flex items-center gap-1 text-red-600">
                  <AlertTriangle className="h-4 w-4" />
                  <span className="font-bold">Offline</span>
                </div>
              </div>
              <div className="flex justify-between">
                <span>Saldo:</span>
                <span className="font-bold">0 Kz</span>
              </div>
              <div className="flex justify-between">
                <span>Última transação:</span>
                <span className="text-sm">Ontem</span>
              </div>
              <div className="flex justify-between items-center">
                <span>Conexão:</span>
                <Wifi className="h-4 w-4 text-red-600" />
              </div>
              <div className="flex justify-between items-center">
                <span>Energia:</span>
                <Battery className="h-4 w-4 text-red-600" />
              </div>
              <div className="bg-red-50 p-2 rounded text-center text-sm text-red-700">
                Em Manutenção
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Atividade dos ATMs (Últimas 24h)</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid grid-cols-4 gap-4 text-sm font-medium border-b pb-2">
              <span>Hora</span>
              <span>ATM</span>
              <span>Operação</span>
              <span>Valor</span>
            </div>
            
            <div className="grid grid-cols-4 gap-4 text-sm">
              <span>14:32</span>
              <span>ATM Principal</span>
              <span>Levantamento</span>
              <span>€ 100,00</span>
            </div>
            
            <div className="grid grid-cols-4 gap-4 text-sm">
              <span>14:15</span>
              <span>ATM Entrada</span>
              <span>Consulta Saldo</span>
              <span>-</span>
            </div>
            
            <div className="grid grid-cols-4 gap-4 text-sm">
              <span>13:58</span>
              <span>ATM Principal</span>
              <span>Levantamento</span>
              <span>€ 200,00</span>
            </div>
            
            <div className="grid grid-cols-4 gap-4 text-sm">
              <span>13:45</span>
              <span>ATM Entrada</span>
              <span>Levantamento</span>
              <span>45.000 Kz</span>
            </div>

            <div className="grid grid-cols-4 gap-4 text-sm">
              <span>13:30</span>
              <span>ATM Principal</span>
              <span>Depósito</span>
              <span>270.000 Kz</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ATM;
