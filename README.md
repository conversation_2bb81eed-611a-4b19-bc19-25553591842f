# Twins_Bank - Sistema Bancário

Sistema bancário completo desenvolvido com React, TypeScript, Node.js e MySQL.

## 🏗️ Arquitetura do Projeto

```
twins-bank/
├── frontend/          # Aplicação React (Frontend)
│   ├── src/
│   ├── public/
│   ├── package.json
│   └── vite.config.ts
├── backend/           # API Node.js (Backend)
│   ├── src/
│   ├── package.json
│   └── server.js
├── package.json       # Scripts do monorepo
└── README.md
```

## 🚀 Tecnologias

### Frontend
- **Framework**: React 18, TypeScript, Tailwind CSS
- **UI Components**: Radix UI, Shadcn/ui
- **Roteamento**: React Router DOM
- **Formulários**: React Hook Form + Zod
- **Estado**: Context API
- **Build**: Vite
- **Linting**: ESLint

### Backend
- **Runtime**: Node.js
- **Framework**: Express.js
- **Base de Dados**: MySQL
- **Autenticação**: JWT
- **Validação**: Joi
- **Logs**: Winston
- **Segurança**: bcrypt, CORS, Helmet

## 📦 Instalação

1. Clone o repositório:
```bash
git clone <url-do-repositorio>
cd twins-bank
```

2. Instale todas as dependências:
```bash
npm run install:all
```

3. Configure as variáveis de ambiente:
```bash
# Backend (.env no diretório backend/)
cp backend/.env.example backend/.env

# Frontend (.env no diretório frontend/)
cp frontend/.env.example frontend/.env
```

4. Execute o projeto completo:
```bash
npm run dev
```

Ou execute separadamente:
```bash
# Apenas Frontend (porta 8080)
npm run dev:frontend

# Apenas Backend (porta 3001)
npm run dev:backend
```

## 🌐 URLs de Acesso

- **Frontend**: http://localhost:8080
- **Backend API**: http://localhost:3001
- **Documentação API**: http://localhost:3001/api-docs

**Use GitHub Codespaces**

- Navigate to the main page of your repository.
- Click on the "Code" button (green button) near the top right.
- Select the "Codespaces" tab.
- Click on "New codespace" to launch a new Codespace environment.
- Edit files directly within the Codespace and commit and push your changes once you're done.

## What technologies are used for this project?

This project is built with:

- Vite
- TypeScript
- React
- shadcn-ui
- Tailwind CSS

## How can I deploy this project?

You can deploy this project using any modern hosting service that supports React applications, such as Vercel, Netlify, or GitHub Pages.
