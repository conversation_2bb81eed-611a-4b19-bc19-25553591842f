# Plano de Implementação do Backend K-Bank

## Objetivo
Desenvolver a arquitetura e implementação completa do backend para o sistema bancário k_bank, seguindo as melhores práticas de desenvolvimento com arquitetura modular, segurança robusta e escalabilidade.

## Escopo
- Sistema de autenticação JWT completo
- API RESTful para todas as funcionalidades do frontend
- Base de dados MySQL normalizada (3NF)
- Sistema RBAC (Role-Based Access Control)
- Operações bancárias completas (caixa, tesouraria, transferências)
- Gestão de clientes (particulares e empresas)
- Sistema de cartões, câmbios, seguros e ATM

## Stack Tecnológica
- **Backend**: Node.js + Express.js
- **Base de Dados**: MySQL
- **Autenticação**: JWT (JSON Web Tokens)
- **Validação**: <PERSON><PERSON> ou <PERSON><PERSON>
- **ORM**: Sequelize ou Prisma
- **Segurança**: bcrypt, helmet, cors
- **Documentação**: Swagger/OpenAPI
- **Testes**: Jest + Supertest

## Arquitetura do Projeto
```
backend/
├── src/
│   ├── auth/           # Autenticação, JWT, middleware
│   ├── users/          # Gestão de utilizadores
│   ├── clients/        # Gestão de clientes (particulares/empresas)
│   ├── accounts/       # Contas bancárias
│   ├── cash-register/  # Operações de caixa
│   ├── treasury/       # Operações de tesouraria
│   ├── transfers/      # Transferências internas/externas
│   ├── cards/          # Gestão de cartões
│   ├── exchange/       # Operações de câmbio
│   ├── insurance/      # Gestão de seguros
│   ├── atm/           # Gestão de ATMs
│   ├── reports/       # Relatórios e analytics
│   ├── core/          # Middleware, validações, utils
│   ├── config/        # Configurações (DB, env)
│   ├── database/      # Migrações, seeds, modelos
│   └── routes/        # Definição de rotas da API
├── tests/             # Testes unitários e integração
├── docs/              # Documentação da API
└── scripts/           # Scripts de deployment e manutenção
```

## Modelo de Dados (Principais Entidades)

### 1. Sistema de Utilizadores e Autenticação
- **roles**: Perfis do sistema (admin, gerente, tesoureiro, caixa, técnico)
- **users**: Utilizadores do sistema
- **user_sessions**: Sessões ativas de utilizadores

### 2. Gestão de Clientes
- **clients**: Clientes do banco (particulares e empresas)
- **client_documents**: Documentos dos clientes
- **client_contacts**: Contactos dos clientes
- **client_addresses**: Endereços dos clientes

### 3. Contas Bancárias
- **accounts**: Contas bancárias
- **account_holders**: Titulares das contas (relação N:N)
- **account_balances**: Histórico de saldos

### 4. Operações de Caixa
- **cash_registers**: Caixas físicos
- **cash_register_sessions**: Sessões de trabalho dos caixas
- **cash_denominations**: Denominações de notas/moedas

### 5. Transações e Movimentos
- **transactions**: Todas as transações do sistema
- **transfers**: Transferências específicas
- **suspended_movements**: Movimentos suspensos

### 6. Módulos Específicos
- **cards**: Cartões bancários
- **atms**: Caixas automáticos
- **insurance_policies**: Apólices de seguro
- **exchange_rates**: Taxas de câmbio
- **tasks**: Tarefas do sistema

## Etapas de Desenvolvimento

### Fase 1: Configuração Inicial (2-3 dias)
- [ ] Configuração do ambiente Node.js + Express
- [ ] Configuração da base de dados MySQL
- [ ] Estrutura de pastas modular
- [ ] Configuração de variáveis de ambiente
- [ ] Setup de ferramentas de desenvolvimento

### Fase 2: Base de Dados (3-4 dias)
- [ ] Criação do schema completo (25+ tabelas)
- [ ] Implementação de migrações
- [ ] Seeds iniciais (roles, super admin)
- [ ] Configuração de relacionamentos e constraints
- [ ] Testes de integridade da base de dados

### Fase 3: Autenticação e Autorização (2-3 dias)
- [ ] Sistema JWT completo
- [ ] Middleware de autenticação
- [ ] Sistema RBAC
- [ ] Endpoints de login/logout
- [ ] Gestão de sessões

### Fase 4: Gestão de Utilizadores (2 dias)
- [ ] CRUD de utilizadores
- [ ] Validações e sanitização
- [ ] Controlo de permissões
- [ ] Endpoints de gestão

### Fase 5: Gestão de Clientes (3-4 dias)
- [ ] Abertura de contas particulares
- [ ] Abertura de contas empresariais
- [ ] Gestão de documentos
- [ ] Validações específicas

### Fase 6: Operações de Caixa (3-4 dias)
- [ ] Abertura/fecho de caixa
- [ ] Gestão de denominações
- [ ] Sessões de trabalho
- [ ] Validações de saldo

### Fase 7: Operações de Tesouraria (3-4 dias)
- [ ] Entrega a caixa
- [ ] Entrega ao cofre
- [ ] Carregamento de ATMs
- [ ] Relatórios de tesouraria

### Fase 8: Sistema de Transferências (4-5 dias)
- [ ] Transferências internas
- [ ] Transferências SPTR/STC
- [ ] Validações de contas
- [ ] Histórico e consultas

### Fase 9: Módulos Complementares (5-6 dias)
- [ ] Sistema de cartões
- [ ] Operações de câmbio
- [ ] Gestão de seguros
- [ ] Gestão de ATMs

### Fase 10: Relatórios e Analytics (2-3 dias)
- [ ] Relatórios financeiros
- [ ] Dashboard analytics
- [ ] Exportação de dados
- [ ] Métricas de performance

### Fase 11: Testes e Documentação (3-4 dias)
- [ ] Testes unitários
- [ ] Testes de integração
- [ ] Documentação da API
- [ ] Guias de deployment

### Fase 12: Deployment e Monitorização (2-3 dias)
- [ ] Configuração de produção
- [ ] Scripts de deployment
- [ ] Monitorização e logs
- [ ] Backup e recovery

## Dependências e Pré-requisitos
- Node.js 18+ instalado
- MySQL 8.0+ configurado
- Acesso à base de dados twins_bank
- Variáveis de ambiente configuradas
- Frontend k_bank funcional para testes

## Estimativa Total
**35-45 dias de desenvolvimento** (7-9 semanas)

## Schema Completo da Base de Dados

### Tabelas do Sistema de Autenticação
```sql
-- Perfis/Roles do sistema
CREATE TABLE roles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Utilizadores do sistema
CREATE TABLE users (
    id CHAR(36) PRIMARY KEY, -- UUID
    full_name VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role_id INT NOT NULL,
    branch_id INT,
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (role_id) REFERENCES roles(id),
    FOREIGN KEY (branch_id) REFERENCES branches(id)
);

-- Sessões de utilizadores
CREATE TABLE user_sessions (
    id CHAR(36) PRIMARY KEY,
    user_id CHAR(36) NOT NULL,
    token_hash VARCHAR(255) NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

### Tabelas de Configuração do Sistema
```sql
-- Balcões/Agências
CREATE TABLE branches (
    id INT PRIMARY KEY AUTO_INCREMENT,
    code VARCHAR(10) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    address TEXT,
    phone VARCHAR(20),
    email VARCHAR(255),
    manager_id CHAR(36),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (manager_id) REFERENCES users(id)
);

-- Moedas suportadas
CREATE TABLE currencies (
    id INT PRIMARY KEY AUTO_INCREMENT,
    code VARCHAR(3) UNIQUE NOT NULL, -- AOA, USD, EUR
    name VARCHAR(50) NOT NULL,
    symbol VARCHAR(5),
    is_active BOOLEAN DEFAULT TRUE
);

-- Taxas de câmbio
CREATE TABLE exchange_rates (
    id INT PRIMARY KEY AUTO_INCREMENT,
    from_currency_id INT NOT NULL,
    to_currency_id INT NOT NULL,
    rate DECIMAL(15,6) NOT NULL,
    effective_date DATE NOT NULL,
    created_by CHAR(36) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (from_currency_id) REFERENCES currencies(id),
    FOREIGN KEY (to_currency_id) REFERENCES currencies(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);
```

### Tabelas de Gestão de Clientes
```sql
-- Clientes (particulares e empresas)
CREATE TABLE clients (
    id CHAR(36) PRIMARY KEY,
    client_type ENUM('individual', 'company') NOT NULL,
    full_name VARCHAR(255), -- Para particulares
    company_name VARCHAR(255), -- Para empresas
    document_type VARCHAR(20), -- BI, Passaporte, NIF
    document_number VARCHAR(50) UNIQUE NOT NULL,
    nif VARCHAR(20) UNIQUE,
    birth_date DATE, -- Para particulares
    incorporation_date DATE, -- Para empresas
    nationality VARCHAR(100),
    gender ENUM('M', 'F'), -- Para particulares
    marital_status VARCHAR(50), -- Para particulares
    profession VARCHAR(100),
    monthly_income DECIMAL(15,2),
    branch_id INT NOT NULL,
    status ENUM('active', 'inactive', 'blocked') DEFAULT 'active',
    created_by CHAR(36) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (branch_id) REFERENCES branches(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- Endereços dos clientes
CREATE TABLE client_addresses (
    id INT PRIMARY KEY AUTO_INCREMENT,
    client_id CHAR(36) NOT NULL,
    address_type ENUM('residential', 'commercial', 'correspondence') NOT NULL,
    province VARCHAR(100),
    municipality VARCHAR(100),
    neighborhood VARCHAR(100),
    street VARCHAR(255),
    house_number VARCHAR(20),
    postal_code VARCHAR(20),
    is_primary BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE CASCADE
);

-- Contactos dos clientes
CREATE TABLE client_contacts (
    id INT PRIMARY KEY AUTO_INCREMENT,
    client_id CHAR(36) NOT NULL,
    contact_type ENUM('phone_personal', 'phone_work', 'email_personal', 'email_work') NOT NULL,
    contact_value VARCHAR(255) NOT NULL,
    is_primary BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE CASCADE
);

-- Documentos dos clientes
CREATE TABLE client_documents (
    id INT PRIMARY KEY AUTO_INCREMENT,
    client_id CHAR(36) NOT NULL,
    document_type VARCHAR(50) NOT NULL, -- BI, Passaporte, Declaração Serviço, etc.
    file_path VARCHAR(500),
    file_name VARCHAR(255),
    upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    uploaded_by CHAR(36) NOT NULL,
    FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE CASCADE,
    FOREIGN KEY (uploaded_by) REFERENCES users(id)
);
```

```

### Tabelas de Contas Bancárias
```sql
-- Contas bancárias
CREATE TABLE accounts (
    id CHAR(36) PRIMARY KEY,
    account_number VARCHAR(20) UNIQUE NOT NULL,
    account_type ENUM('corrente', 'poupanca', 'salario', 'junior') NOT NULL,
    currency_id INT NOT NULL,
    balance DECIMAL(15,2) DEFAULT 0.00,
    available_balance DECIMAL(15,2) DEFAULT 0.00,
    overdraft_limit DECIMAL(15,2) DEFAULT 0.00,
    status ENUM('active', 'inactive', 'blocked', 'closed') DEFAULT 'active',
    opening_date DATE NOT NULL,
    closing_date DATE NULL,
    branch_id INT NOT NULL,
    created_by CHAR(36) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (currency_id) REFERENCES currencies(id),
    FOREIGN KEY (branch_id) REFERENCES branches(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- Titulares das contas (relação N:N)
CREATE TABLE account_holders (
    id INT PRIMARY KEY AUTO_INCREMENT,
    account_id CHAR(36) NOT NULL,
    client_id CHAR(36) NOT NULL,
    holder_type ENUM('primary', 'secondary') NOT NULL,
    signature_path VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (account_id) REFERENCES accounts(id) ON DELETE CASCADE,
    FOREIGN KEY (client_id) REFERENCES clients(id),
    UNIQUE KEY unique_account_client (account_id, client_id)
);

-- Histórico de saldos
CREATE TABLE account_balances (
    id INT PRIMARY KEY AUTO_INCREMENT,
    account_id CHAR(36) NOT NULL,
    balance DECIMAL(15,2) NOT NULL,
    available_balance DECIMAL(15,2) NOT NULL,
    balance_date DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (account_id) REFERENCES accounts(id) ON DELETE CASCADE,
    UNIQUE KEY unique_account_date (account_id, balance_date)
);
```

### Tabelas de Operações de Caixa
```sql
-- Caixas físicos
CREATE TABLE cash_registers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    register_number VARCHAR(10) UNIQUE NOT NULL,
    description TEXT,
    branch_id INT NOT NULL,
    status ENUM('available', 'in_use', 'closed', 'maintenance') DEFAULT 'available',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (branch_id) REFERENCES branches(id)
);

-- Sessões de trabalho dos caixas
CREATE TABLE cash_register_sessions (
    id CHAR(36) PRIMARY KEY,
    user_id CHAR(36) NOT NULL,
    cash_register_id INT NOT NULL,
    opening_balance DECIMAL(15,2) NOT NULL,
    closing_balance_declared DECIMAL(15,2) NULL,
    closing_balance_validated DECIMAL(15,2) NULL,
    status ENUM('open', 'closed', 'validated') DEFAULT 'open',
    opened_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    closed_at TIMESTAMP NULL,
    validated_at TIMESTAMP NULL,
    validated_by CHAR(36) NULL,
    notes TEXT,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (cash_register_id) REFERENCES cash_registers(id),
    FOREIGN KEY (validated_by) REFERENCES users(id)
);

-- Denominações de notas/moedas
CREATE TABLE cash_denominations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    session_id CHAR(36) NOT NULL,
    denomination_type ENUM('note', 'coin') NOT NULL,
    denomination_value DECIMAL(10,2) NOT NULL,
    quantity INT NOT NULL,
    total_value DECIMAL(15,2) NOT NULL,
    operation_type ENUM('opening', 'closing') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (session_id) REFERENCES cash_register_sessions(id) ON DELETE CASCADE
);
```

### Tabelas de Transações
```sql
-- Transações principais
CREATE TABLE transactions (
    id CHAR(36) PRIMARY KEY,
    transaction_code VARCHAR(20) UNIQUE NOT NULL,
    transaction_type ENUM('transfer', 'deposit', 'withdrawal', 'exchange', 'fee', 'treasury') NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    currency_id INT NOT NULL,
    source_account_id CHAR(36) NULL,
    destination_account_id CHAR(36) NULL,
    source_entity VARCHAR(100) NULL, -- cofre, cash_register_id:5, atm_id:2
    destination_entity VARCHAR(100) NULL,
    description TEXT,
    reference_number VARCHAR(50),
    status ENUM('pending', 'processing', 'completed', 'failed', 'cancelled') DEFAULT 'pending',
    processed_by CHAR(36) NOT NULL,
    session_id CHAR(36) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP NULL,
    FOREIGN KEY (currency_id) REFERENCES currencies(id),
    FOREIGN KEY (source_account_id) REFERENCES accounts(id),
    FOREIGN KEY (destination_account_id) REFERENCES accounts(id),
    FOREIGN KEY (processed_by) REFERENCES users(id),
    FOREIGN KEY (session_id) REFERENCES cash_register_sessions(id)
);

-- Transferências específicas
CREATE TABLE transfers (
    id CHAR(36) PRIMARY KEY,
    transaction_id CHAR(36) NOT NULL,
    transfer_type ENUM('internal', 'sptr', 'stc') NOT NULL,
    source_account_number VARCHAR(20) NOT NULL,
    destination_account_number VARCHAR(20) NOT NULL,
    beneficiary_name VARCHAR(255),
    beneficiary_bank VARCHAR(255), -- Para transferências externas
    swift_code VARCHAR(20), -- Para transferências internacionais
    purpose_code VARCHAR(10),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (transaction_id) REFERENCES transactions(id) ON DELETE CASCADE
);

-- Movimentos suspensos
CREATE TABLE suspended_movements (
    id CHAR(36) PRIMARY KEY,
    transaction_id CHAR(36) NOT NULL,
    suspension_reason TEXT NOT NULL,
    suspension_type ENUM('compliance', 'limit_exceeded', 'documentation', 'fraud_alert') NOT NULL,
    suspended_by CHAR(36) NOT NULL,
    suspended_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    resolved_by CHAR(36) NULL,
    resolved_at TIMESTAMP NULL,
    resolution_notes TEXT,
    status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
    FOREIGN KEY (transaction_id) REFERENCES transactions(id),
    FOREIGN KEY (suspended_by) REFERENCES users(id),
    FOREIGN KEY (resolved_by) REFERENCES users(id)
);
```

```

### Tabelas de Cartões
```sql
-- Cartões bancários
CREATE TABLE cards (
    id CHAR(36) PRIMARY KEY,
    card_number VARCHAR(19) UNIQUE NOT NULL, -- Encriptado
    card_type ENUM('debit', 'credit', 'prepaid') NOT NULL,
    account_id CHAR(36) NOT NULL,
    holder_name VARCHAR(255) NOT NULL,
    expiry_date DATE NOT NULL,
    cvv_hash VARCHAR(255) NOT NULL, -- Hash do CVV
    pin_hash VARCHAR(255), -- Hash do PIN
    daily_limit DECIMAL(15,2) DEFAULT 0.00,
    monthly_limit DECIMAL(15,2) DEFAULT 0.00,
    status ENUM('active', 'blocked', 'expired', 'cancelled') DEFAULT 'active',
    issued_date DATE NOT NULL,
    issued_by CHAR(36) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (account_id) REFERENCES accounts(id),
    FOREIGN KEY (issued_by) REFERENCES users(id)
);

-- Transações de cartões
CREATE TABLE card_transactions (
    id CHAR(36) PRIMARY KEY,
    card_id CHAR(36) NOT NULL,
    transaction_id CHAR(36) NOT NULL,
    merchant_name VARCHAR(255),
    merchant_category VARCHAR(100),
    terminal_id VARCHAR(50),
    authorization_code VARCHAR(20),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (card_id) REFERENCES cards(id),
    FOREIGN KEY (transaction_id) REFERENCES transactions(id)
);
```

### Tabelas de ATMs
```sql
-- Caixas automáticos
CREATE TABLE atms (
    id INT PRIMARY KEY AUTO_INCREMENT,
    atm_code VARCHAR(20) UNIQUE NOT NULL,
    location VARCHAR(255) NOT NULL,
    branch_id INT NOT NULL,
    cash_capacity DECIMAL(15,2) NOT NULL,
    current_balance DECIMAL(15,2) DEFAULT 0.00,
    status ENUM('online', 'offline', 'maintenance', 'out_of_cash') DEFAULT 'online',
    last_maintenance DATE,
    installed_date DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (branch_id) REFERENCES branches(id)
);

-- Carregamentos de ATM
CREATE TABLE atm_loadings (
    id CHAR(36) PRIMARY KEY,
    atm_id INT NOT NULL,
    loaded_amount DECIMAL(15,2) NOT NULL,
    loaded_by CHAR(36) NOT NULL,
    loading_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    notes TEXT,
    FOREIGN KEY (atm_id) REFERENCES atms(id),
    FOREIGN KEY (loaded_by) REFERENCES users(id)
);
```

### Tabelas de Seguros
```sql
-- Apólices de seguro
CREATE TABLE insurance_policies (
    id CHAR(36) PRIMARY KEY,
    policy_number VARCHAR(50) UNIQUE NOT NULL,
    client_id CHAR(36) NOT NULL,
    policy_type VARCHAR(100) NOT NULL,
    coverage_amount DECIMAL(15,2) NOT NULL,
    premium_amount DECIMAL(15,2) NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    status ENUM('active', 'expired', 'cancelled', 'suspended') DEFAULT 'active',
    created_by CHAR(36) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (client_id) REFERENCES clients(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);
```

### Tabelas de Sistema
```sql
-- Tarefas do sistema
CREATE TABLE tasks (
    id CHAR(36) PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    assigned_to CHAR(36) NOT NULL,
    created_by CHAR(36) NOT NULL,
    priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
    status ENUM('pending', 'in_progress', 'completed', 'cancelled') DEFAULT 'pending',
    due_date DATE,
    completed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (assigned_to) REFERENCES users(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- Logs de auditoria
CREATE TABLE audit_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id CHAR(36),
    action VARCHAR(100) NOT NULL,
    table_name VARCHAR(100),
    record_id VARCHAR(36),
    old_values JSON,
    new_values JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- Configurações do sistema
CREATE TABLE system_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    description TEXT,
    updated_by CHAR(36),
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (updated_by) REFERENCES users(id)
);
```

## API Endpoints Principais

### Autenticação
- `POST /auth/login` - Login de utilizador
- `POST /auth/logout` - Logout de utilizador
- `POST /auth/refresh` - Renovar token JWT
- `GET /auth/me` - Dados do utilizador logado

### Utilizadores
- `GET /users` - Listar utilizadores
- `POST /users` - Criar utilizador
- `GET /users/:id` - Obter utilizador
- `PUT /users/:id` - Actualizar utilizador
- `DELETE /users/:id` - Desactivar utilizador

### Clientes
- `GET /clients` - Listar clientes
- `POST /clients/individual` - Criar cliente particular
- `POST /clients/company` - Criar cliente empresa
- `GET /clients/:id` - Obter cliente
- `PUT /clients/:id` - Actualizar cliente

### Contas
- `GET /accounts` - Listar contas
- `POST /accounts` - Criar conta
- `GET /accounts/:id` - Obter conta
- `GET /accounts/:id/balance` - Saldo da conta
- `GET /accounts/:id/transactions` - Transações da conta

### Caixa
- `GET /cash-registers/available` - Caixas disponíveis
- `POST /cash-register/sessions/open` - Abrir sessão de caixa
- `POST /cash-register/sessions/close` - Fechar sessão de caixa
- `GET /cash-register/sessions` - Listar sessões

### Transferências
- `POST /transfers/internal` - Transferência interna
- `POST /transfers/sptr` - Transferência SPTR
- `POST /transfers/stc` - Transferência STC
- `GET /transfers` - Consultar transferências

## Próximos Passos Imediatos
1. Configurar ambiente de desenvolvimento
2. Criar estrutura de pastas do backend
3. Implementar schema completo da base de dados
4. Configurar sistema de autenticação básico
5. Implementar primeiros endpoints de teste
