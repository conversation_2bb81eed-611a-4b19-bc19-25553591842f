# K-Bank Security Code Samples

**Versão:** 1.0  
**Data:** 08/09/2025  
**Objetivo:** Exemplos de código para implementar as correções de segurança identificadas

---

## 1. Autenticação com Cookies HttpOnly

### ANTES (Vulnerável - localStorage)

<augment_code_snippet path="frontend/src/utils/tokenManager.ts" mode="EXCERPT">
````typescript
// VULNERÁVEL: Tokens acessíveis via JavaScript
public storeTokenData(tokenData: TokenData): void {
  localStorage.setItem(TOKEN_KEY, tokenData.accessToken);
  localStorage.setItem(REFRESH_TOKEN_KEY, tokenData.refreshToken);
  localStorage.setItem(SESSION_ID_KEY, tokenData.sessionId);
}
````
</augment_code_snippet>

### DEPOIS (Seguro - Cookies HttpOnly)

```javascript
// backend/src/auth/cookieAuth.js (NOVO)
const cookieOptions = {
  httpOnly: true, // Não acessível via JavaScript
  secure: process.env.NODE_ENV === 'production', // HTTPS apenas em produção
  sameSite: 'strict', // Proteção CSRF
  maxAge: 24 * 60 * 60 * 1000, // 24 horas
  path: '/' // Disponível em toda a aplicação
};

const setAuthCookie = (res, token, refreshToken) => {
  res.cookie('auth-token', token, cookieOptions);
  res.cookie('refresh-token', refreshToken, {
    ...cookieOptions,
    maxAge: 7 * 24 * 60 * 60 * 1000 // 7 dias para refresh
  });
};

const clearAuthCookies = (res) => {
  res.clearCookie('auth-token', cookieOptions);
  res.clearCookie('refresh-token', cookieOptions);
};

module.exports = { setAuthCookie, clearAuthCookies };
```

```javascript
// backend/src/routes/authRoutes.js (ATUALIZADO)
const { setAuthCookie, clearAuthCookies } = require('../auth/cookieAuth');

// Login endpoint atualizado
router.post('/login', catchAsync(async (req, res, next) => {
  // ... validação e autenticação existente ...
  
  const tokenData = await generateTokenPair(user);
  
  // Definir cookies seguros em vez de retornar tokens
  setAuthCookie(res, tokenData.accessToken, tokenData.refreshToken);
  
  res.status(200).json({
    status: 'success',
    message: 'Login realizado com sucesso',
    data: {
      user: {
        id: user.id,
        full_name: user.full_name,
        email: user.email,
        role: user.role_name,
        // ... outros dados do utilizador
      }
      // Não retornar tokens na resposta
    }
  });
}));

// Logout endpoint atualizado
router.post('/logout', catchAsync(async (req, res, next) => {
  clearAuthCookies(res);
  
  res.status(200).json({
    status: 'success',
    message: 'Logout realizado com sucesso'
  });
}));
```

```javascript
// backend/src/auth/middleware.js (ATUALIZADO)
const authenticate = catchAsync(async (req, res, next) => {
  // Obter token do cookie em vez do header
  const token = req.cookies['auth-token'];
  
  if (!token) {
    return next(new AppError('Token de acesso necessário', 401, 'NO_TOKEN'));
  }
  
  // ... resto da validação permanece igual
});
```

```typescript
// frontend/src/services/authService.ts (ATUALIZADO)
export const authService = {
  async login(credentials: LoginRequest): Promise<LoginResponse> {
    const response = await makeRequest<LoginResponse>(
      API_ENDPOINTS.AUTH.LOGIN,
      {
        method: 'POST',
        body: JSON.stringify(credentials),
        credentials: 'include' // Incluir cookies nas requisições
      }
    );
    
    // Não armazenar tokens - são geridos automaticamente via cookies
    return response;
  },

  isAuthenticated(): boolean {
    // Verificar se cookie existe (método simplificado)
    return document.cookie.includes('auth-token');
  }
};
```

---

## 2. Proteção CSRF

### ANTES (Vulnerável - Sem proteção CSRF)

```javascript
// Qualquer POST/PUT/DELETE sem validação CSRF
router.post('/transfer', authenticate, (req, res) => {
  // VULNERÁVEL: Pode ser executado via CSRF
  const { fromAccount, toAccount, amount } = req.body;
  // ... processar transferência
});
```

### DEPOIS (Seguro - Com proteção CSRF)

```javascript
// backend/src/middleware/csrf.js (NOVO)
const csrf = require('csurf');

const csrfProtection = csrf({
  cookie: {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict'
  }
});

// Middleware para fornecer token CSRF
const provideCsrfToken = (req, res, next) => {
  res.locals.csrfToken = req.csrfToken();
  next();
};

module.exports = { csrfProtection, provideCsrfToken };
```

```javascript
// backend/src/server.js (ATUALIZADO)
const { csrfProtection, provideCsrfToken } = require('./middleware/csrf');

// Aplicar proteção CSRF a todas as rotas protegidas
app.use('/api', authenticate, csrfProtection, provideCsrfToken);
```

```javascript
// backend/src/routes/transferRoutes.js (ATUALIZADO)
router.post('/transfer', catchAsync(async (req, res, next) => {
  // CSRF token é validado automaticamente pelo middleware
  const { fromAccount, toAccount, amount } = req.body;
  
  // Log de auditoria para operação sensível
  logger.audit('Transfer initiated', {
    userId: req.user.id,
    fromAccount,
    toAccount,
    amount,
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });
  
  // ... processar transferência com segurança
}));

// Endpoint para obter token CSRF
router.get('/csrf-token', (req, res) => {
  res.json({
    status: 'success',
    data: {
      csrfToken: res.locals.csrfToken
    }
  });
});
```

```typescript
// frontend/src/services/apiClient.ts (NOVO)
class ApiClient {
  private csrfToken: string | null = null;

  async getCsrfToken(): Promise<string> {
    if (!this.csrfToken) {
      const response = await fetch('/api/csrf-token', {
        credentials: 'include'
      });
      const data = await response.json();
      this.csrfToken = data.data.csrfToken;
    }
    return this.csrfToken;
  }

  async makeSecureRequest(url: string, options: RequestInit = {}) {
    const csrfToken = await this.getCsrfToken();
    
    return fetch(url, {
      ...options,
      credentials: 'include',
      headers: {
        ...options.headers,
        'X-CSRF-Token': csrfToken,
        'Content-Type': 'application/json'
      }
    });
  }
}

export const apiClient = new ApiClient();
```

---

## 3. Content Security Policy (CSP)

### ANTES (Vulnerável - Sem CSP)

```javascript
// Sem proteção contra XSS
app.use(helmet());
```

### DEPOIS (Seguro - Com CSP restritiva)

```javascript
// backend/src/middleware/security.js (NOVO)
const helmet = require('helmet');
const crypto = require('crypto');

const generateNonce = () => crypto.randomBytes(16).toString('base64');

const securityMiddleware = (req, res, next) => {
  const nonce = generateNonce();
  res.locals.nonce = nonce;
  
  helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        scriptSrc: [
          "'self'",
          `'nonce-${nonce}'`,
          // Permitir apenas scripts com nonce
        ],
        styleSrc: [
          "'self'",
          "'unsafe-inline'", // Para CSS-in-JS (pode ser removido com nonce)
          "https://fonts.googleapis.com"
        ],
        imgSrc: [
          "'self'",
          "data:",
          "https:"
        ],
        connectSrc: [
          "'self'",
          process.env.NODE_ENV === 'development' ? 'ws://localhost:*' : ''
        ].filter(Boolean),
        fontSrc: [
          "'self'",
          "https://fonts.gstatic.com"
        ],
        objectSrc: ["'none'"],
        mediaSrc: ["'self'"],
        frameSrc: ["'none'"],
        baseUri: ["'self'"],
        formAction: ["'self'"]
      },
    },
    hsts: {
      maxAge: 31536000, // 1 ano
      includeSubDomains: true,
      preload: true
    },
    noSniff: true,
    xssFilter: true,
    referrerPolicy: { policy: "strict-origin-when-cross-origin" }
  })(req, res, next);
};

module.exports = { securityMiddleware };
```

---

## 4. Sanitização de Input

### ANTES (Vulnerável - Input não sanitizado)

<augment_code_snippet path="backend/src/routes/userRoutes.js" mode="EXCERPT">
````javascript
if (search) {
  whereConditions.push('(u.full_name LIKE ? OR u.email LIKE ?)');
  queryParams.push(`%${search}%`, `%${search}%`);
}
````
</augment_code_snippet>

### DEPOIS (Seguro - Input sanitizado)

```javascript
// backend/src/utils/sanitizer.js (NOVO)
const validator = require('validator');
const xss = require('xss');

const sanitizeInput = {
  // Sanitizar texto geral
  text: (input) => {
    if (!input || typeof input !== 'string') return '';
    return validator.escape(input.trim());
  },

  // Sanitizar pesquisa (remover caracteres especiais SQL)
  search: (input) => {
    if (!input || typeof input !== 'string') return '';
    return input
      .trim()
      .replace(/[%_\\]/g, '\\$&') // Escapar wildcards SQL
      .replace(/[<>'"]/g, '') // Remover caracteres perigosos
      .substring(0, 100); // Limitar tamanho
  },

  // Sanitizar HTML (para campos que permitem formatação)
  html: (input) => {
    if (!input || typeof input !== 'string') return '';
    return xss(input, {
      whiteList: {
        b: [],
        i: [],
        u: [],
        strong: [],
        em: []
      }
    });
  },

  // Sanitizar email
  email: (input) => {
    if (!input || typeof input !== 'string') return '';
    const email = input.trim().toLowerCase();
    return validator.isEmail(email) ? email : '';
  },

  // Sanitizar números
  number: (input, min = 0, max = Number.MAX_SAFE_INTEGER) => {
    const num = parseFloat(input);
    if (isNaN(num)) return 0;
    return Math.max(min, Math.min(max, num));
  }
};

module.exports = sanitizeInput;
```

```javascript
// backend/src/routes/userRoutes.js (ATUALIZADO)
const sanitizeInput = require('../utils/sanitizer');

router.get('/', authorize('admin', 'gerente'), catchAsync(async (req, res) => {
  const { 
    page = 1, 
    limit = 10, 
    search = '', 
    role = '', 
    branch = '', 
    active = '' 
  } = req.query;
  
  // Sanitizar todos os inputs
  const sanitizedSearch = sanitizeInput.search(search);
  const sanitizedRole = sanitizeInput.text(role);
  const sanitizedPage = sanitizeInput.number(page, 1, 1000);
  const sanitizedLimit = sanitizeInput.number(limit, 1, 100);
  
  const offset = (sanitizedPage - 1) * sanitizedLimit;
  
  let whereConditions = [];
  let queryParams = [];
  
  if (sanitizedSearch) {
    whereConditions.push('(u.full_name LIKE ? OR u.email LIKE ?)');
    queryParams.push(`%${sanitizedSearch}%`, `%${sanitizedSearch}%`);
  }
  
  if (sanitizedRole) {
    whereConditions.push('r.name = ?');
    queryParams.push(sanitizedRole);
  }
  
  // ... resto da implementação
}));
```

---

## 5. Geração de Segredos Seguros

### ANTES (Vulnerável - Segredos fracos)

```env
# .env.example (VULNERÁVEL)
JWT_SECRET=your_super_secret_jwt_key_here_change_in_production
SESSION_SECRET=your_session_secret_here
```

### DEPOIS (Seguro - Segredos criptograficamente seguros)

```javascript
// scripts/generate-secrets.js (NOVO)
const crypto = require('crypto');
const fs = require('fs');
const path = require('path');

const generateSecrets = () => {
  const secrets = {
    JWT_SECRET: crypto.randomBytes(64).toString('hex'),
    SESSION_SECRET: crypto.randomBytes(32).toString('hex'),
    ENCRYPTION_KEY: crypto.randomBytes(32).toString('hex'),
    CSRF_SECRET: crypto.randomBytes(32).toString('hex')
  };

  console.log('Segredos gerados com segurança:');
  console.log('=====================================');
  Object.entries(secrets).forEach(([key, value]) => {
    console.log(`${key}=${value}`);
  });
  console.log('=====================================');
  console.log('IMPORTANTE: Armazene estes segredos de forma segura!');
  
  return secrets;
};

// Validar força dos segredos
const validateSecrets = () => {
  const requiredSecrets = ['JWT_SECRET', 'SESSION_SECRET'];
  const missingSecrets = [];
  const weakSecrets = [];

  requiredSecrets.forEach(secret => {
    const value = process.env[secret];
    if (!value) {
      missingSecrets.push(secret);
    } else if (value.length < 32) {
      weakSecrets.push(secret);
    } else if (value.includes('your_') || value.includes('change_')) {
      weakSecrets.push(`${secret} (usando valor de exemplo)`);
    }
  });

  if (missingSecrets.length > 0) {
    throw new Error(`Segredos em falta: ${missingSecrets.join(', ')}`);
  }

  if (weakSecrets.length > 0) {
    throw new Error(`Segredos fracos detectados: ${weakSecrets.join(', ')}`);
  }

  console.log('✅ Todos os segredos são criptograficamente seguros');
};

module.exports = { generateSecrets, validateSecrets };

// Executar se chamado diretamente
if (require.main === module) {
  generateSecrets();
}
```

```javascript
// backend/src/server.js (ATUALIZADO)
const { validateSecrets } = require('../scripts/generate-secrets');

// Validar segredos no startup
try {
  validateSecrets();
} catch (error) {
  logger.error('Erro de configuração de segurança:', error.message);
  logger.error('Execute: node scripts/generate-secrets.js');
  process.exit(1);
}
```

```env
# .env.example (ATUALIZADO)
# IMPORTANTE: NÃO use estes valores em produção!
# Execute 'node scripts/generate-secrets.js' para gerar segredos seguros

# Configuração da Base de Dados
DB_HOST=localhost
DB_PORT=3306
DB_NAME=twins_bank
DB_USER=your_db_user
DB_PASSWORD=your_db_password

# Configuração JWT - GERAR SEGREDOS SEGUROS!
JWT_SECRET=EXECUTE_generate-secrets.js_PARA_GERAR
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# Configuração de Sessão - GERAR SEGREDOS SEGUROS!
SESSION_SECRET=EXECUTE_generate-secrets.js_PARA_GERAR

# Configuração do Servidor
PORT=3001
NODE_ENV=development

# AVISO: Valores de exemplo - substituir em produção!
```

---

## 6. Logging de Auditoria Seguro

### ANTES (Insuficiente - Logging básico)

```javascript
logger.info(`${req.method} ${req.path} - ${req.ip}`);
```

### DEPOIS (Seguro - Auditoria abrangente)

```javascript
// backend/src/middleware/audit.js (NOVO)
const logger = require('../core/logger');

const auditLogger = (action, resource, options = {}) => {
  return (req, res, next) => {
    const auditData = {
      // Dados do utilizador (sem informações sensíveis)
      userId: req.user?.id,
      userEmail: req.user?.email,
      userRole: req.user?.role_name,
      
      // Dados da ação
      action,
      resource,
      method: req.method,
      path: req.path,
      
      // Dados da requisição (sanitizados)
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      referer: req.get('Referer'),
      
      // Dados de contexto
      timestamp: new Date().toISOString(),
      sessionId: req.sessionID,
      
      // Dados específicos (se fornecidos)
      ...options.extraData
    };

    // Log antes da ação
    logger.audit(`${action} initiated`, auditData);

    // Interceptar resposta para log de resultado
    const originalSend = res.send;
    res.send = function(data) {
      const responseData = {
        ...auditData,
        statusCode: res.statusCode,
        success: res.statusCode < 400,
        responseTime: Date.now() - req.startTime
      };

      if (res.statusCode >= 400) {
        logger.audit(`${action} failed`, responseData);
      } else {
        logger.audit(`${action} completed`, responseData);
      }

      return originalSend.call(this, data);
    };

    next();
  };
};

// Middleware para operações sensíveis
const sensitiveOperationAudit = (operation) => {
  return auditLogger('SENSITIVE_OPERATION', operation, {
    extraData: { sensitiveOperation: true }
  });
};

module.exports = { auditLogger, sensitiveOperationAudit };
```

```javascript
// backend/src/routes/transferRoutes.js (ATUALIZADO)
const { sensitiveOperationAudit } = require('../middleware/audit');

// Aplicar auditoria a operações sensíveis
router.post('/transfer', 
  sensitiveOperationAudit('MONEY_TRANSFER'),
  catchAsync(async (req, res, next) => {
    const { fromAccount, toAccount, amount } = req.body;
    
    // Dados adicionais para auditoria (sem informações sensíveis)
    req.auditData = {
      fromAccountId: fromAccount,
      toAccountId: toAccount,
      amount: amount,
      currency: req.body.currency || 'EUR'
    };
    
    // ... processar transferência
  })
);
```

---

**Próximo:** Implementar estas correções seguindo o cronograma do SECURITY_IMPLEMENTATION_ROADMAP.md
