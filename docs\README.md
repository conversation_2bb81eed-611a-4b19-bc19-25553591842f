# 📚 Documentação do Projeto Twins_Bank

Este diretório contém documentação histórica e de desenvolvimento do projeto.

## 📋 Conteúdo

### Documentos de Planeamento
- **`etapas.md`** - Plano original de implementação do backend
- **`instrucoes.md`** - Diretrizes técnicas e de arquitetura do frontend

### Documentos Ativos (na raiz do projeto)
- **`README.md`** - Documentação principal do projeto
- **`actualizacoes.md`** - Changelog detalhado do projeto
- **`BACKEND_INTEGRATION_ROADMAP.md`** - Roadmap de integração backend

## 🗂️ Estrutura de Documentação

```
docs/
├── README.md           # Este arquivo
├── etapas.md          # Plano histórico de implementação
└── instrucoes.md      # Diretrizes de desenvolvimento

/ (raiz)
├── README.md                        # Documentação principal
├── actualizacoes.md                # Changelog do projeto
└── BACKEND_INTEGRATION_ROADMAP.md  # Roadmap de desenvolvimento
```

## 📝 Notas

- Os documentos neste diretório são principalmente históricos
- Para documentação atual, consulte os arquivos na raiz do projeto
- Para documentação específica do backend, veja `backend/README.md`
- Para documentação específica do frontend, veja `frontend/README.md` (se existir)
