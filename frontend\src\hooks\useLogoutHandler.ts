import { useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useToast } from '@/hooks/use-toast';
import authService from '@/services/authService';

// Tipos de logout para diferentes mensagens
export enum LogoutReason {
  MANUAL = 'manual',
  SESSION_TIMEOUT = 'session_timeout',
  SECURITY = 'security',
  TOKEN_EXPIRED = 'token_expired',
  UNAUTHORIZED = 'unauthorized',
  SERVER_ERROR = 'server_error'
}

// Interface para configuração de logout
interface LogoutConfig {
  reason: LogoutReason;
  message?: string;
  redirectTo?: string;
  showToast?: boolean;
}

// Hook personalizado para gerenciar logout com notificações
export const useLogoutHandler = () => {
  const navigate = useNavigate();
  const { toast } = useToast();

  // Mensagens padrão para cada tipo de logout
  const getLogoutMessage = (reason: LogoutReason): { title: string; description: string } => {
    switch (reason) {
      case LogoutReason.MANUAL:
        return {
          title: "Logout realizado",
          description: "Você foi deslogado com sucesso. Até breve!"
        };
      
      case LogoutReason.SESSION_TIMEOUT:
        return {
          title: "Sessão expirada",
          description: "Sua sessão expirou por inatividade. Faça login novamente para continuar."
        };
      
      case LogoutReason.SECURITY:
        return {
          title: "Logout por segurança",
          description: "Você foi deslogado por questões de segurança. Faça login novamente."
        };
      
      case LogoutReason.TOKEN_EXPIRED:
        return {
          title: "Token expirado",
          description: "Seu token de acesso expirou. Faça login novamente para continuar."
        };
      
      case LogoutReason.UNAUTHORIZED:
        return {
          title: "Acesso não autorizado",
          description: "Você não tem permissão para acessar este recurso. Faça login novamente."
        };
      
      case LogoutReason.SERVER_ERROR:
        return {
          title: "Erro do servidor",
          description: "Ocorreu um erro no servidor. Você foi deslogado por segurança."
        };
      
      default:
        return {
          title: "Logout realizado",
          description: "Você foi deslogado do sistema. Faça login novamente."
        };
    }
  };

  // Função principal de logout
  const handleLogout = useCallback(async (config: LogoutConfig) => {
    const {
      reason,
      message,
      redirectTo = '/login',
      showToast = true
    } = config;

    try {
      // Realizar logout no serviço de autenticação
      await authService.logout();
    } catch (error) {
      console.error('Erro durante logout:', error);
      // Mesmo com erro, continuar com o processo de logout local
    }

    // Mostrar notificação se solicitado
    if (showToast) {
      const logoutMessage = getLogoutMessage(reason);
      const finalMessage = message || logoutMessage.description;

      // Determinar variante do toast baseado no motivo
      const variant = reason === LogoutReason.MANUAL ? 'default' : 'destructive';

      toast({
        title: logoutMessage.title,
        description: finalMessage,
        variant,
        duration: reason === LogoutReason.MANUAL ? 3000 : 5000, // Mais tempo para erros
      });
    }

    // Aguardar um pouco para o usuário ver a notificação
    setTimeout(() => {
      navigate(redirectTo, { replace: true });
    }, 500);

  }, [navigate, toast]);

  // Funções de conveniência para diferentes tipos de logout
  const logoutManual = useCallback(() => {
    handleLogout({ reason: LogoutReason.MANUAL });
  }, [handleLogout]);

  const logoutSessionTimeout = useCallback(() => {
    handleLogout({ reason: LogoutReason.SESSION_TIMEOUT });
  }, [handleLogout]);

  const logoutSecurity = useCallback(() => {
    handleLogout({ reason: LogoutReason.SECURITY });
  }, [handleLogout]);

  const logoutTokenExpired = useCallback(() => {
    handleLogout({ reason: LogoutReason.TOKEN_EXPIRED });
  }, [handleLogout]);

  const logoutUnauthorized = useCallback(() => {
    handleLogout({ reason: LogoutReason.UNAUTHORIZED });
  }, [handleLogout]);

  const logoutServerError = useCallback(() => {
    handleLogout({ reason: LogoutReason.SERVER_ERROR });
  }, [handleLogout]);

  // Função para detectar tipo de erro e fazer logout apropriado
  const handleAuthError = useCallback((error: any) => {
    console.error('Erro de autenticação detectado:', error);

    // Analisar o erro para determinar o tipo de logout
    if (error?.code === 'TOKEN_EXPIRED' || error?.statusCode === 401) {
      if (error?.message?.includes('inativo') || error?.message?.includes('não encontrado')) {
        logoutSecurity();
      } else {
        logoutTokenExpired();
      }
    } else if (error?.statusCode === 403) {
      logoutUnauthorized();
    } else if (error?.statusCode >= 500) {
      logoutServerError();
    } else {
      // Erro genérico - assumir problema de segurança
      logoutSecurity();
    }
  }, [logoutSecurity, logoutTokenExpired, logoutUnauthorized, logoutServerError]);

  return {
    handleLogout,
    logoutManual,
    logoutSessionTimeout,
    logoutSecurity,
    logoutTokenExpired,
    logoutUnauthorized,
    logoutServerError,
    handleAuthError
  };
};

export default useLogoutHandler;
