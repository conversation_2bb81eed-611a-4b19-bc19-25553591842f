const express = require('express');
const { authorize } = require('../auth/middleware');

const router = express.Router();

// Placeholder routes for treasury operations
// TODO: Implement full treasury functionality

/**
 * POST /api/treasury/deliver-to-cash
 * Entrega a caixa
 */
router.post('/deliver-to-cash', authorize('tesoureiro'), (req, res) => {
  res.status(201).json({
    status: 'success',
    message: 'Entrega a caixa - Em desenvolvimento'
  });
});

/**
 * POST /api/treasury/deliver-to-vault
 * Entrega ao cofre
 */
router.post('/deliver-to-vault', authorize('tesoureiro'), (req, res) => {
  res.status(201).json({
    status: 'success',
    message: 'Entrega ao cofre - Em desenvolvimento'
  });
});

/**
 * POST /api/treasury/load-atm
 * Carregamento de ATM
 */
router.post('/load-atm', authorize('tesoureiro'), (req, res) => {
  res.status(201).json({
    status: 'success',
    message: 'Carregamento de ATM - Em desenvolvimento'
  });
});

module.exports = router;
