# Configuração da Base de Dados
DB_HOST=localhost
DB_PORT=3306
DB_NAME=twins_bank
DB_USER=your_db_user
DB_PASSWORD=your_db_password

# Configuração JWT
JWT_SECRET=your_super_secret_jwt_key_here_change_in_production
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# Configuração do Servidor
PORT=3001
NODE_ENV=development

# Configuração de CORS
CORS_ORIGIN=http://localhost:5173

# Configuração de Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Configuração de Upload de Ficheiros
UPLOAD_MAX_SIZE=5242880
UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,application/pdf

# Configuração de Logs
LOG_LEVEL=info
LOG_FILE=logs/app.log

# Configuração de Email (para notificações)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_email_password

# Configuração de Segurança
BCRYPT_ROUNDS=12
SESSION_SECRET=your_session_secret_here

# Configuração de Backup
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
