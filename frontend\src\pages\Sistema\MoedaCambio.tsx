import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { DollarSign, AlertTriangle } from 'lucide-react';

const MoedaCambio = () => {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <DollarSign className="h-8 w-8" />
            Moeda / Câmbio
          </h1>
          <p className="text-gray-600">Configuração de moedas e taxas de câmbio</p>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-yellow-500" />
            Funcionalidade em Desenvolvimento
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-12">
            <DollarSign className="h-24 w-24 mx-auto text-gray-300 mb-4" />
            <h3 className="text-xl font-semibold text-gray-700 mb-2">
              Configuração de Moedas e Câmbio
            </h3>
            <p className="text-gray-500 mb-6 max-w-md mx-auto">
              Esta funcionalidade permitirá configurar moedas aceitas pelo sistema e definir taxas de câmbio atualizadas.
            </p>
            
            <div className="bg-white p-4 rounded-lg border">
              <h4 className="font-medium text-gray-800 mb-2">Funcionalidades Previstas:</h4>
              <ul className="space-y-1 text-sm text-gray-600">
                <li>• Cadastro de moedas</li>
                <li>• Configuração de taxas de câmbio</li>
                <li>• Atualização automática de cotações</li>
                <li>• Histórico de variações</li>
                <li>• Alertas de flutuação</li>
              </ul>
            </div>

            <Button disabled className="w-full mt-6" size="lg">
              Funcionalidade Indisponível
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default MoedaCambio;
