import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { FolderOpen, AlertTriangle } from 'lucide-react';

const CaixasAbertos = () => {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <FolderOpen className="h-8 w-8" />
            Caixas Abertos
          </h1>
          <p className="text-gray-600">Monitoramento de caixas em operação</p>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-yellow-500" />
            Funcionalidade em Desenvolvimento
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-12">
            <FolderOpen className="h-24 w-24 mx-auto text-gray-300 mb-4" />
            <h3 className="text-xl font-semibold text-gray-700 mb-2">
              Monitoramento de Caixas Abertos
            </h3>
            <p className="text-gray-500 mb-6 max-w-md mx-auto">
              Esta funcionalidade permitirá monitorar em tempo real todos os caixas que estão em operação no sistema.
            </p>
            
            <div className="bg-white p-4 rounded-lg border">
              <h4 className="font-medium text-gray-800 mb-2">Funcionalidades Previstas:</h4>
              <ul className="space-y-1 text-sm text-gray-600">
                <li>• Status em tempo real dos caixas</li>
                <li>• Operadores ativos</li>
                <li>• Saldos atuais</li>
                <li>• Tempo de operação</li>
                <li>• Alertas de inatividade</li>
              </ul>
            </div>

            <Button disabled className="w-full mt-6" size="lg">
              Funcionalidade Indisponível
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default CaixasAbertos;
