import { API_CONFIG, API_ENDPOINTS } from '@/config/api';

export interface AccountApplication {
  id?: string;
  client_id: string;
  account_type: 'corrente' | 'poupanca' | 'salario' | 'junior';
  currency_id: number;
  initial_deposit?: number;
  overdraft_limit?: number;
  branch_id: number;
  status?: 'pending' | 'approved' | 'rejected' | 'active';
  created_at?: string;
  updated_at?: string;
}

export interface Account {
  id: string;
  account_number: string;
  account_type: 'corrente' | 'poupanca' | 'salario' | 'junior';
  currency_id: number;
  balance: number;
  available_balance: number;
  overdraft_limit: number;
  status: 'active' | 'inactive' | 'blocked' | 'closed';
  opening_date: string;
  closing_date?: string;
  branch_id: number;
  branch_name?: string;
  created_by: string;
  created_at: string;
  updated_at: string;
  holders?: AccountHolder[];
}

export interface AccountHolder {
  id: number;
  account_id: string;
  client_id: string;
  client_name?: string;
  holder_type: 'primary' | 'secondary';
  signature_path?: string;
  created_at: string;
}

export interface CreateAccountRequest {
  client_id: string;
  account_type: 'corrente' | 'poupanca' | 'salario' | 'junior';
  currency_id: number;
  initial_deposit?: number;
  overdraft_limit?: number;
  branch_id: number;
  additional_holders?: {
    client_id: string;
    holder_type: 'secondary';
  }[];
}

export interface AccountListResponse {
  accounts: Account[];
  pagination: {
    current_page: number;
    total_pages: number;
    total_records: number;
    records_per_page: number;
    has_next: boolean;
    has_previous: boolean;
  };
}

export interface AccountFilters {
  search?: string;
  account_type?: string;
  status?: string;
  branch_id?: number;
  client_id?: string;
  start_date?: string;
  end_date?: string;
  page?: number;
  limit?: number;
}

class AccountService {
  private getAuthHeaders() {
    const token = localStorage.getItem('twins-bank-token');
    return {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };
  }

  /**
   * Criar nova conta bancária
   */
  async createAccount(accountData: CreateAccountRequest): Promise<Account> {
    const url = `${API_CONFIG.baseURL}${API_ENDPOINTS.ACCOUNTS.CREATE}`;
    const response = await fetch(url, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(accountData)
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `Erro ao criar conta: ${response.statusText}`);
    }

    const result = await response.json();
    return result.data.account;
  }

  /**
   * Listar contas com filtros
   */
  async getAccounts(filters: AccountFilters = {}): Promise<AccountListResponse> {
    const params = new URLSearchParams();
    
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        params.append(key, value.toString());
      }
    });

    const endpoint = `${API_ENDPOINTS.ACCOUNTS.LIST}?${params.toString()}`;
    const response = await makeRequest<AccountListResponse>(endpoint);
    return response.data!;
  }

  /**
   * Obter conta específica por ID
   */
  async getAccount(id: string): Promise<Account> {
    const response = await makeRequest<{ account: Account }>({
      method: 'GET',
      url: API_ENDPOINTS.ACCOUNTS.UPDATE(id)
    });
    return response.data!.account;
  }

  /**
   * Obter contas de um cliente específico
   */
  async getClientAccounts(clientId: string): Promise<Account[]> {
    const response = await this.getAccounts({ client_id: clientId, limit: 100 });
    return response.accounts;
  }

  /**
   * Atualizar conta
   */
  async updateAccount(id: string, updates: Partial<Account>): Promise<Account> {
    const response = await makeRequest<{ account: Account }>({
      method: 'PUT',
      url: API_ENDPOINTS.ACCOUNTS.UPDATE(id),
      data: updates
    });
    return response.data!.account;
  }

  /**
   * Bloquear conta
   */
  async blockAccount(id: string, reason?: string): Promise<Account> {
    return this.updateAccount(id, { 
      status: 'blocked',
      // Note: In a real implementation, you'd also send the reason
    });
  }

  /**
   * Desbloquear conta
   */
  async unblockAccount(id: string): Promise<Account> {
    return this.updateAccount(id, { status: 'active' });
  }

  /**
   * Fechar conta
   */
  async closeAccount(id: string): Promise<Account> {
    return this.updateAccount(id, { 
      status: 'closed',
      closing_date: new Date().toISOString().split('T')[0]
    });
  }

  /**
   * Obter tipos de conta disponíveis
   */
  getAccountTypes() {
    return [
      { value: 'corrente', label: 'Conta Corrente', description: 'Para movimentações diárias' },
      { value: 'poupanca', label: 'Conta Poupança', description: 'Para poupanças com rendimento' },
      { value: 'salario', label: 'Conta Salário', description: 'Para recebimento de salário' },
      { value: 'junior', label: 'Conta Jovem', description: 'Para clientes até 25 anos' }
    ];
  }

  /**
   * Validar dados de criação de conta
   */
  validateAccountData(data: CreateAccountRequest): string[] {
    const errors: string[] = [];

    if (!data.client_id) {
      errors.push('Cliente é obrigatório');
    }

    if (!data.account_type) {
      errors.push('Tipo de conta é obrigatório');
    }

    if (!data.currency_id) {
      errors.push('Moeda é obrigatória');
    }

    if (!data.branch_id) {
      errors.push('Balcão é obrigatório');
    }

    if (data.initial_deposit && data.initial_deposit < 0) {
      errors.push('Depósito inicial não pode ser negativo');
    }

    if (data.overdraft_limit && data.overdraft_limit < 0) {
      errors.push('Limite de descoberto não pode ser negativo');
    }

    // Validações específicas por tipo de conta
    if (data.account_type === 'junior') {
      // Em uma implementação real, verificaríamos a idade do cliente
      // errors.push('Cliente deve ter até 25 anos para conta jovem');
    }

    if (data.account_type === 'salario' && data.overdraft_limit && data.overdraft_limit > 0) {
      errors.push('Conta salário não pode ter limite de descoberto');
    }

    return errors;
  }

  /**
   * Gerar número de conta (simulação)
   */
  generateAccountNumber(): string {
    const timestamp = Date.now().toString();
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `${timestamp.slice(-7)}${random}`;
  }

  /**
   * Calcular taxas de manutenção por tipo de conta
   */
  getMaintenanceFees() {
    return {
      corrente: 2500, // 2.500 AOA
      poupanca: 0,    // Gratuita
      salario: 0,     // Gratuita
      junior: 0       // Gratuita
    };
  }

  /**
   * Obter limites padrão por tipo de conta
   */
  getDefaultLimits() {
    return {
      corrente: {
        daily_withdrawal: 500000,    // 500.000 AOA
        daily_transfer: 1000000,     // 1.000.000 AOA
        overdraft_limit: 100000      // 100.000 AOA
      },
      poupanca: {
        daily_withdrawal: 200000,    // 200.000 AOA
        daily_transfer: 500000,      // 500.000 AOA
        overdraft_limit: 0           // Sem descoberto
      },
      salario: {
        daily_withdrawal: 300000,    // 300.000 AOA
        daily_transfer: 500000,      // 500.000 AOA
        overdraft_limit: 0           // Sem descoberto
      },
      junior: {
        daily_withdrawal: 100000,    // 100.000 AOA
        daily_transfer: 200000,      // 200.000 AOA
        overdraft_limit: 0           // Sem descoberto
      }
    };
  }
}

export const accountService = new AccountService();
