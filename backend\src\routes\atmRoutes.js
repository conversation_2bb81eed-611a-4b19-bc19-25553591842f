const express = require('express');
const { authorize } = require('../auth/middleware');

const router = express.Router();

// Placeholder routes for ATM management
// TODO: Implement full ATM functionality

/**
 * GET /api/atm
 * Listar ATMs
 */
router.get('/', authorize('admin', 'gerente', 'tesoureiro'), (req, res) => {
  res.status(200).json({
    status: 'success',
    message: 'Listagem de ATMs - Em desenvolvimento',
    data: []
  });
});

/**
 * POST /api/atm/load
 * Carregar ATM
 */
router.post('/load', authorize('tesoureiro'), (req, res) => {
  res.status(201).json({
    status: 'success',
    message: 'Carregamento de ATM - Em desenvolvimento'
  });
});

module.exports = router;
