/**
 * Utilitários de formatação de data e hora para o sistema K-Bank
 * Padronização: DD/MM/YYYY HH:MM (timezone Angola - WAT UTC+1)
 */

// Configurações padrão para Angola
const ANGOLA_TIMEZONE = 'Africa/Luanda';
const ANGOLA_LOCALE = 'pt-AO';

/**
 * Converte uma data UTC para o timezone de Angola
 * @param date - Data em formato UTC (string ISO ou objeto Date)
 * @returns Objeto Date ajustado para timezone de Angola
 */
export const convertToAngolaTime = (date: string | Date | null): Date | null => {
  if (!date) return null;
  
  try {
    const dateObj = date instanceof Date ? date : new Date(date);
    
    // Verificar se a data é válida
    if (isNaN(dateObj.getTime())) {
      console.warn('Data inválida fornecida:', date);
      return null;
    }
    
    return dateObj;
  } catch (error) {
    console.error('Erro ao converter data:', error);
    return null;
  }
};

/**
 * Formatar data no padrão DD/MM/YYYY
 * @param date - Data para formatar
 * @returns String formatada ou 'N/A' se inválida
 */
export const formatDate = (date: string | Date | null): string => {
  const convertedDate = convertToAngolaTime(date);
  if (!convertedDate) return 'N/A';
  
  return convertedDate.toLocaleDateString(ANGOLA_LOCALE, {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    timeZone: ANGOLA_TIMEZONE
  });
};

/**
 * Formatar hora no padrão HH:MM
 * @param date - Data para extrair a hora
 * @returns String formatada ou 'N/A' se inválida
 */
export const formatTime = (date: string | Date | null): string => {
  const convertedDate = convertToAngolaTime(date);
  if (!convertedDate) return 'N/A';
  
  return convertedDate.toLocaleTimeString(ANGOLA_LOCALE, {
    hour: '2-digit',
    minute: '2-digit',
    timeZone: ANGOLA_TIMEZONE
  });
};

/**
 * Formatar data e hora no padrão DD/MM/YYYY HH:MM
 * @param date - Data para formatar
 * @returns String formatada ou 'N/A' se inválida
 */
export const formatDateTime = (date: string | Date | null): string => {
  const convertedDate = convertToAngolaTime(date);
  if (!convertedDate) return 'N/A';
  
  return convertedDate.toLocaleString(ANGOLA_LOCALE, {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    timeZone: ANGOLA_TIMEZONE
  });
};

/**
 * Formatar data e hora com segundos no padrão DD/MM/YYYY HH:MM:SS
 * @param date - Data para formatar
 * @returns String formatada ou 'N/A' se inválida
 */
export const formatDateTimeWithSeconds = (date: string | Date | null): string => {
  const convertedDate = convertToAngolaTime(date);
  if (!convertedDate) return 'N/A';
  
  return convertedDate.toLocaleString(ANGOLA_LOCALE, {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    timeZone: ANGOLA_TIMEZONE
  });
};

/**
 * Formatar data por extenso (ex: "Segunda-feira, 8 de Setembro de 2025")
 * @param date - Data para formatar
 * @returns String formatada ou 'N/A' se inválida
 */
export const formatDateLong = (date: string | Date | null): string => {
  const convertedDate = convertToAngolaTime(date);
  if (!convertedDate) return 'N/A';
  
  return convertedDate.toLocaleDateString(ANGOLA_LOCALE, {
    weekday: 'long',
    day: 'numeric',
    month: 'long',
    year: 'numeric',
    timeZone: ANGOLA_TIMEZONE
  });
};

/**
 * Obter data e hora atual no timezone de Angola
 * @returns Objeto Date atual ajustado para Angola
 */
export const getCurrentAngolaTime = (): Date => {
  return new Date();
};

/**
 * Formatar data e hora atual no padrão DD-MM-YYYY HH:MM:SS (para header)
 * @returns String formatada da data/hora atual
 */
export const getCurrentDateTimeForHeader = (): string => {
  const now = getCurrentAngolaTime();
  
  // Obter componentes da data em timezone de Angola
  const day = now.toLocaleDateString(ANGOLA_LOCALE, { 
    day: '2-digit', 
    timeZone: ANGOLA_TIMEZONE 
  });
  const month = now.toLocaleDateString(ANGOLA_LOCALE, { 
    month: '2-digit', 
    timeZone: ANGOLA_TIMEZONE 
  });
  const year = now.toLocaleDateString(ANGOLA_LOCALE, { 
    year: 'numeric', 
    timeZone: ANGOLA_TIMEZONE 
  });
  const time = now.toLocaleTimeString(ANGOLA_LOCALE, {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    timeZone: ANGOLA_TIMEZONE
  });
  
  return `${day}-${month}-${year} ${time}`;
};

/**
 * Verificar se uma data é válida
 * @param date - Data para verificar
 * @returns true se válida, false caso contrário
 */
export const isValidDate = (date: string | Date | null): boolean => {
  if (!date) return false;
  
  try {
    const dateObj = date instanceof Date ? date : new Date(date);
    return !isNaN(dateObj.getTime());
  } catch {
    return false;
  }
};

/**
 * Calcular diferença em dias entre duas datas
 * @param date1 - Primeira data
 * @param date2 - Segunda data (padrão: hoje)
 * @returns Número de dias de diferença
 */
export const getDaysDifference = (
  date1: string | Date, 
  date2: string | Date = new Date()
): number => {
  const d1 = convertToAngolaTime(date1);
  const d2 = convertToAngolaTime(date2);
  
  if (!d1 || !d2) return 0;
  
  const diffTime = Math.abs(d2.getTime() - d1.getTime());
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
};

/**
 * Formatar data relativa (ex: "há 2 dias", "hoje", "ontem")
 * @param date - Data para formatar
 * @returns String com formato relativo
 */
export const formatRelativeDate = (date: string | Date | null): string => {
  const convertedDate = convertToAngolaTime(date);
  if (!convertedDate) return 'N/A';
  
  const now = getCurrentAngolaTime();
  const diffDays = getDaysDifference(convertedDate, now);
  
  if (diffDays === 0) return 'Hoje';
  if (diffDays === 1) return 'Ontem';
  if (diffDays < 7) return `Há ${diffDays} dias`;
  if (diffDays < 30) return `Há ${Math.floor(diffDays / 7)} semanas`;
  if (diffDays < 365) return `Há ${Math.floor(diffDays / 30)} meses`;
  
  return `Há ${Math.floor(diffDays / 365)} anos`;
};

// Exportar configurações para uso em outros componentes
export const DATE_CONFIG = {
  TIMEZONE: ANGOLA_TIMEZONE,
  LOCALE: ANGOLA_LOCALE,
  FORMATS: {
    DATE: 'DD/MM/YYYY',
    TIME: 'HH:MM',
    DATETIME: 'DD/MM/YYYY HH:MM',
    DATETIME_WITH_SECONDS: 'DD/MM/YYYY HH:MM:SS',
    HEADER: 'DD-MM-YYYY HH:MM:SS'
  }
} as const;
