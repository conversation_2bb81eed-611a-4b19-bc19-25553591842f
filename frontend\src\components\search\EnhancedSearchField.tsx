import React, { useState, useEffect } from 'react';
import { Search } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import AdvancedSearchModal from './AdvancedSearchModal';

interface FilterCriteria {
  searchTerm: string;
  dateRange: {
    from: string;
    to: string;
  };
  transactionType: string;
  amountRange: {
    min: string;
    max: string;
  };
  user: string;
  status: string;
  accountType: string;
}

interface EnhancedSearchFieldProps {
  onSearch?: (criteria: FilterCriteria) => void;
  placeholder?: string;
  className?: string;
}

const EnhancedSearchField: React.FC<EnhancedSearchFieldProps> = ({
  onSearch,
  placeholder = "Pesquisar clientes, contas, transações...",
  className = ""
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [activeFilters, setActiveFilters] = useState<FilterCriteria>({
    searchTerm: '',
    dateRange: { from: '', to: '' },
    transactionType: '',
    amountRange: { min: '', max: '' },
    user: '',
    status: '',
    accountType: '',
  });

  // Calcular número de filtros ativos
  const getActiveFilterCount = () => {
    let count = 0;
    if (activeFilters.dateRange.from || activeFilters.dateRange.to) count++;
    if (activeFilters.transactionType) count++;
    if (activeFilters.amountRange.min || activeFilters.amountRange.max) count++;
    if (activeFilters.user) count++;
    if (activeFilters.status) count++;
    if (activeFilters.accountType) count++;
    return count;
  };

  // Atualizar termo de pesquisa nos filtros quando o input principal mudar
  useEffect(() => {
    setActiveFilters(prev => ({
      ...prev,
      searchTerm: searchTerm
    }));
  }, [searchTerm]);

  // Executar pesquisa quando filtros mudarem
  useEffect(() => {
    if (onSearch) {
      onSearch(activeFilters);
    }
  }, [activeFilters, onSearch]);

  const handleAdvancedSearch = (criteria: FilterCriteria) => {
    setActiveFilters(criteria);
    setSearchTerm(criteria.searchTerm);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  const getActiveFilterLabels = () => {
    const labels = [];
    
    if (activeFilters.dateRange.from || activeFilters.dateRange.to) {
      const from = activeFilters.dateRange.from ? new Date(activeFilters.dateRange.from).toLocaleDateString('pt-AO') : '';
      const to = activeFilters.dateRange.to ? new Date(activeFilters.dateRange.to).toLocaleDateString('pt-AO') : '';
      if (from && to) {
        labels.push(`Data: ${from} - ${to}`);
      } else if (from) {
        labels.push(`Data: a partir de ${from}`);
      } else if (to) {
        labels.push(`Data: até ${to}`);
      }
    }
    
    if (activeFilters.transactionType) {
      labels.push(`Tipo: ${activeFilters.transactionType}`);
    }
    
    if (activeFilters.amountRange.min || activeFilters.amountRange.max) {
      const min = activeFilters.amountRange.min ? `${activeFilters.amountRange.min} Kz` : '';
      const max = activeFilters.amountRange.max ? `${activeFilters.amountRange.max} Kz` : '';
      if (min && max) {
        labels.push(`Valor: ${min} - ${max}`);
      } else if (min) {
        labels.push(`Valor: a partir de ${min}`);
      } else if (max) {
        labels.push(`Valor: até ${max}`);
      }
    }
    
    if (activeFilters.user) {
      labels.push(`Usuário: ${activeFilters.user}`);
    }
    
    if (activeFilters.status) {
      labels.push(`Status: ${activeFilters.status}`);
    }
    
    if (activeFilters.accountType) {
      labels.push(`Conta: ${activeFilters.accountType}`);
    }
    
    return labels;
  };

  const clearFilter = (filterType: string) => {
    const newFilters = { ...activeFilters };
    
    switch (filterType) {
      case 'date':
        newFilters.dateRange = { from: '', to: '' };
        break;
      case 'transactionType':
        newFilters.transactionType = '';
        break;
      case 'amount':
        newFilters.amountRange = { min: '', max: '' };
        break;
      case 'user':
        newFilters.user = '';
        break;
      case 'status':
        newFilters.status = '';
        break;
      case 'accountType':
        newFilters.accountType = '';
        break;
    }
    
    setActiveFilters(newFilters);
  };

  return (
    <div className={`space-y-2 ${className}`}>
      {/* Campo de pesquisa principal */}
      <div className="relative flex items-center">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
        <Input
          placeholder={placeholder}
          className="pl-10 pr-12 bg-gray-50 border-gray-200 focus:bg-white dark:bg-gray-800 dark:border-gray-700 dark:focus:bg-gray-900"
          value={searchTerm}
          onChange={handleInputChange}
        />
        <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
          <AdvancedSearchModal 
            onSearch={handleAdvancedSearch}
            activeFilters={getActiveFilterCount()}
          />
        </div>
      </div>

      {/* Badges de filtros ativos */}
      {getActiveFilterCount() > 0 && (
        <div className="flex flex-wrap gap-2">
          {getActiveFilterLabels().map((label, index) => {
            const filterType = label.split(':')[0].toLowerCase();
            let filterKey = '';
            
            if (filterType.includes('data')) filterKey = 'date';
            else if (filterType.includes('tipo')) filterKey = 'transactionType';
            else if (filterType.includes('valor')) filterKey = 'amount';
            else if (filterType.includes('usuário')) filterKey = 'user';
            else if (filterType.includes('status')) filterKey = 'status';
            else if (filterType.includes('conta')) filterKey = 'accountType';
            
            return (
              <Badge 
                key={index} 
                variant="secondary" 
                className="text-xs bg-twins-accent/20 text-twins-primary hover:bg-twins-accent/30 cursor-pointer"
                onClick={() => clearFilter(filterKey)}
              >
                {label}
                <span className="ml-1 hover:text-red-600">×</span>
              </Badge>
            );
          })}
        </div>
      )}
    </div>
  );
};

export default EnhancedSearchField;
