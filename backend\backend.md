
---

### **Prompt Detalhado para Implementação de Backend do Sistema de Gestão de Caixa**

**Objetivo:** Desenvolver a arquitetura e a implementação do backend para o sistema bancario k_bank. O sistema deve ser seguro, escalável e seguir as melhores práticas de desenvolvimento, incluindo uma estrutura de código limpa e modular.

---

### **Fase 1: Planeamento e Arquitetura**

**1.1. Análise dos Perfis de Utilizador e Regras de Negócio**

O sistema terá 4 perfis de utilizador com permissões distintas. A sua tarefa é modelar o sistema de controlo de acesso baseado em funções (RBAC) para os seguintes perfis:

*   **Admin (Super Administrador):**
    *   Gestão completa de utilizadores (criar, editar, desativar qualquer perfil).
    *   Acesso e gestão de configurações globais do sistema.
    *   Pode visualizar todos os relatórios e logs do sistema.
    *   Este perfil é para gestão de alto nível do sistema em si.

*   **Técnico:**
    *   Acesso a logs técnicos para diagnóstico de problemas.
    *   Gestão de configurações técnicas do sistema (ex: integrações, base de dados).
    *   Não tem acesso a operações financeiras (não pode mover dinheiro).
    *   Pode ser visto como um "Admin" com foco em TI, sem permissões de negócio.

*   **Gerente:**
    *   Perfil operacional de mais alto nível.
    *   Herda **todas** as permissões do Tesoureiro e do Caixa.
    *   Pode visualizar relatórios consolidados de toda a operação.
    *   Pode realizar auditorias e estornos (sujeito a regras de negócio a definir).
    *   Pode gerir a configuração das unidades de caixa (ex: criar um novo caixa físico no sistema).

*   **Tesoureiro:**
    *   Responsável pela gestão do cofre principal.
    *   **Funções:**
        *   Carregar caixas no início do expediente (transferir valor do cofre para o caixa).
        *   Receber valores do fecho de caixa (transferir valor do caixa para o cofre).
        *   Carregar ATMs.
        *   Gerar relatórios de movimentações de todos os caixas e do cofre.
        *   Validar e confirmar os valores do fecho de caixa declarados pelos operadores.

*   **Caixa:**
    *   Perfil operacional da linha da frente.
    *   **Funções:**
        *   Iniciar uma sessão de trabalho ("Abrir Caixa"), recebendo um valor inicial do Tesoureiro.
        *   Realizar transações (este será o próximo escopo, por agora, foque na abertura e fecho).
        *   Encerrar a sessão de trabalho ("Fechar Caixa"), declarando o valor final.
        *   Transferir o valor do fecho para o Tesoureiro.

**1.2. Arquitetura do Código**
*   **Princípio:** Adote uma arquitetura modular (ou *feature-sliced*). Todo o código relacionado a uma funcionalidade principal deve residir no seu próprio diretório.
*   **Estrutura de Pastas Sugerida:**
    ```
    /src
    ├── /auth         # Lógica de autenticação, JWT, senhas
    ├── /users        # Gestão de utilizadores (CRUD)
    ├── /cash-register # Lógica do Caixa (abrir, fechar, sessão)
    ├── /treasury     # Lógica do Tesoureiro (cofre, transferências, relatórios)
    ├── /management   # Lógica do Gerente (dashboard, configurações)
    ├── /core         # Lógica central, middleware, gestão de erros
    ├── /config       # Ficheiros de configuração (db, env)
    ├── /database     # Migrações, seeds, modelos de dados
    └── /routes       # Definição de todas as rotas da API
    ```

---

### **Fase 2: Base de Dados e Configuração Inicial**

**2.1. Conexão e Configuração da Base de Dados**
*   Utilize a ferramenta `mcp tool` e a conexão `twins_db` para todas as operações de base de dados.
*   Configure as credenciais de acesso à base de dados exclusivamente através de variáveis de ambiente (`.env`). **Não inclua credenciais diretamente no código.**

**2.2. Schema da Base de Dados**
Crie as seguintes tabelas SQL, garantindo o uso de chaves estrangeiras para manter a integridade relacional.

*   `roles`:
    *   `id` (PK, INT, AUTO_INCREMENT)
    *   `name` (VARCHAR, UNIQUE, NOT NULL) - ex: 'admin', 'gerente', 'tesoureiro', 'caixa'

*   `users`:
    *   `id` (PK, UUID)
    *   `full_name` (VARCHAR)
    *   `email` (VARCHAR, UNIQUE, NOT NULL)
    *   `password_hash` (VARCHAR, NOT NULL) - **Nunca armazene senhas em texto plano. Use um algoritmo forte como bcrypt.**
    *   `role_id` (FK para `roles.id`)
    *   `is_active` (BOOLEAN, DEFAULT true)
    *   `created_at`, `updated_at` (TIMESTAMPS)

*   `cash_registers`: (Representa os caixas físicos)
    *   `id` (PK, INT, AUTO_INCREMENT)
    *   `register_number` (VARCHAR, UNIQUE)
    *   `description` (TEXT)
    *   `status` (ENUM('available', 'in_use', 'closed'), DEFAULT 'available')

*   `cash_register_sessions`: (Regista cada turno de um operador)
    *   `id` (PK, UUID)
    *   `user_id` (FK para `users.id`) - O operador de caixa.
    *   `cash_register_id` (FK para `cash_registers.id`)
    *   `opening_balance` (DECIMAL) - Valor recebido do tesoureiro.
    *   `closing_balance_declared` (DECIMAL, NULLABLE) - Valor que o caixa diz ter.
    *   `closing_balance_validated` (DECIMAL, NULLABLE) - Valor confirmado pelo tesoureiro.
    *   `status` (ENUM('open', 'closed', 'validated'), DEFAULT 'open')
    *   `opened_at`, `closed_at`, `validated_at` (TIMESTAMPS)

*   `transactions`: (Rastreia todas as movimentações de dinheiro)
    *   `id` (PK, UUID)
    *   `type` (ENUM('COFRE_PARA_CAIXA', 'CAIXA_PARA_COFRE', 'CARGA_ATM'))
    *   `amount` (DECIMAL)
    *   `source_entity` (VARCHAR, NULLABLE) - ex: 'cofre', 'cash_register_id:5'
    *   `destination_entity` (VARCHAR, NULLABLE) - ex: 'cash_register_id:5', 'atm_id:2'
    *   `user_id` (FK para `users.id`) - Quem autorizou a transação (o Tesoureiro).
    *   `session_id` (FK para `cash_register_sessions.id`, NULLABLE)
    *   `notes` (TEXT)
    *   `created_at` (TIMESTAMP)

**2.3. Seed (Povoamento) Inicial da Base de Dados**
*   Crie um script de "seeding" para povoar a tabela `roles`.
*   Crie o utilizador Super Admin conforme solicitado. O script deve:
    1.  Inserir na tabela `users` um registo com:
        *   `email`: `<EMAIL>`
        *   `password_hash`: **O hash bcrypt de '123456789'**
        *   `role_id`: O ID correspondente ao perfil 'admin'.
    2.  Este script deve ser executado apenas uma vez na configuração inicial do sistema.

---

### **Fase 3: Plano de Implementação da API (Endpoints)**

Implemente uma API RESTful seguindo a estrutura de pastas definida. Utilize JWT (JSON Web Tokens) para a gestão de autenticação e middleware para a verificação de perfis em rotas protegidas.

**Etapa 1: Autenticação e Utilizadores (Core)**
*   `POST /auth/login`: Recebe email e senha, valida, e retorna um token JWT com o ID e o perfil do utilizador.
*   `POST /users`: (Admin) Cria um novo utilizador.
*   `GET /users`: (Admin) Lista todos os utilizadores.
*   `GET /users/me`: (Autenticado) Retorna os dados do utilizador logado.

**Etapa 2: Fluxo do Caixa**
*   `POST /cash-register/sessions/open`: (Caixa) Inicia uma sessão. Requer `cash_register_id` e `opening_balance` (que deve ter sido previamente definido por uma transação do tesoureiro).
*   `POST /cash-register/sessions/close`: (Caixa) Fecha a sessão ativa. Requer `closing_balance_declared`.

**Etapa 3: Fluxo do Tesoureiro**
*   `POST /treasury/transactions/to-cash-register`: (Tesoureiro) Regista uma transferência do cofre para um caixa.
*   `POST /treasury/transactions/from-cash-register`: (Tesoureiro) Regista o recebimento de valor de um caixa que fechou.
*   `POST /treasury/sessions/:sessionId/validate`: (Tesoureiro) Valida o fecho de uma sessão, inserindo o `closing_balance_validated`.
*   `GET /treasury/reports/transactions`: (Tesoureiro, Gerente) Retorna um relatório de transações, com filtros por data, tipo e utilizador.

**Etapa 4: Fluxo do Gerente e Admin**
*   Implementar as rotas de gestão que herdam as funcionalidades acima.
*   `GET /management/dashboard`: (Gerente) Retorna dados agregados do estado atual de todos os caixas.
*   `PUT /users/:id`: (Admin) Atualiza os dados de um utilizador.
*   `DELETE /users/:id`: (Admin) Desativa um utilizador.

---

### **Fase 4: Instruções Finais e Dúvidas**

*   **Questão em aberto:** A lógica de como um Caixa "escolhe o número do seu caixa" precisa ser definida. A API deve fornecer uma lista de caixas com `status = 'available'` para o Caixa selecionar no frontend? Ou é uma atribuição feita pelo Gerente?
    *   **Ação:** Antes de implementar o endpoint `POST /cash-register/sessions/open`, por favor, responda com a sua sugestão para esta lógica de negócio. A minha sugestão inicial é: o Caixa solicita uma lista de caixas disponíveis (`GET /cash-registers/available`) e seleciona um ID para a abertura da sessão.

*   **Lembrete de Segurança:** Remova quaisquer dados de teste "mocados" ou hardcoded do código final. Toda a configuração deve vir de variáveis de ambiente.

Avance com a implementação seguindo este plano. Comece pela configuração do ambiente e da base de dados. Se tiver alguma outra dúvida sobre as regras de negócio, pergunte antes de prosseguir com a codificação.