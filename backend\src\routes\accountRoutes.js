const express = require('express');
const { authorize } = require('../auth/middleware');
const { executeQuery } = require('../config/database');
const logger = require('../core/logger');

const router = express.Router();

/**
 * Listar contas com filtros e paginação
 * GET /api/accounts
 */
router.get('/', authorize('admin', 'gerente', 'caixa'), async (req, res) => {
  try {
    const {
      search = '',
      account_type = '',
      status = '',
      branch_id = '',
      client_id = '',
      start_date = '',
      end_date = '',
      page = 1,
      limit = 20
    } = req.query;

    const offset = (parseInt(page) - 1) * parseInt(limit);

    // Construir query base
    let whereConditions = ['1=1'];
    let queryParams = [];

    // Filtros
    if (search) {
      whereConditions.push(`(a.account_number LIKE ? OR c.full_name LIKE ? OR c.company_name LIKE ?)`);
      const searchPattern = `%${search}%`;
      queryParams.push(searchPattern, searchPattern, searchPattern);
    }

    if (account_type) {
      whereConditions.push(`a.account_type = ?`);
      queryParams.push(account_type);
    }

    if (status) {
      whereConditions.push(`a.status = ?`);
      queryParams.push(status);
    }

    if (branch_id) {
      whereConditions.push(`a.branch_id = ?`);
      queryParams.push(parseInt(branch_id));
    }

    if (client_id) {
      whereConditions.push(`ah.client_id = ?`);
      queryParams.push(client_id);
    }

    if (start_date) {
      whereConditions.push(`DATE(a.opening_date) >= ?`);
      queryParams.push(start_date);
    }

    if (end_date) {
      whereConditions.push(`DATE(a.opening_date) <= ?`);
      queryParams.push(end_date);
    }

    const whereClause = whereConditions.join(' AND ');

    // Query para contar total de registros
    const countQuery = `
      SELECT COUNT(DISTINCT a.id) as total
      FROM accounts a
      LEFT JOIN account_holders ah ON a.id = ah.account_id AND ah.holder_type = 'primary'
      LEFT JOIN clients c ON ah.client_id = c.id
      LEFT JOIN branches b ON a.branch_id = b.id
      WHERE ${whereClause}
    `;

    const countResult = await executeQuery(countQuery, queryParams);
    const totalRecords = countResult[0].total;
    const totalPages = Math.ceil(totalRecords / parseInt(limit));

    // Query principal para buscar contas
    const accountsQuery = `
      SELECT
        a.*,
        b.name as branch_name,
        c.full_name as primary_holder_name,
        c.company_name as primary_holder_company,
        c.client_type as primary_holder_type,
        curr.code as currency_code,
        curr.symbol as currency_symbol
      FROM accounts a
      LEFT JOIN account_holders ah ON a.id = ah.account_id AND ah.holder_type = 'primary'
      LEFT JOIN clients c ON ah.client_id = c.id
      LEFT JOIN branches b ON a.branch_id = b.id
      LEFT JOIN currencies curr ON a.currency_id = curr.id
      WHERE ${whereClause}
      ORDER BY a.created_at DESC
      LIMIT ? OFFSET ?
    `;

    const accounts = await executeQuery(accountsQuery, [...queryParams, parseInt(limit), offset]);

    // Para cada conta, buscar todos os titulares
    for (let account of accounts) {
      const holdersQuery = `
        SELECT
          ah.*,
          c.full_name,
          c.company_name,
          c.client_type,
          c.document_type,
          c.document_number
        FROM account_holders ah
        JOIN clients c ON ah.client_id = c.id
        WHERE ah.account_id = ?
        ORDER BY ah.holder_type, ah.created_at
      `;

      account.holders = await executeQuery(holdersQuery, [account.id]);
    }

    res.status(200).json({
      status: 'success',
      data: {
        accounts,
        pagination: {
          current_page: parseInt(page),
          total_pages: totalPages,
          total_records: totalRecords,
          records_per_page: parseInt(limit),
          has_next: parseInt(page) < totalPages,
          has_previous: parseInt(page) > 1
        }
      }
    });

  } catch (error) {
    logger.error('Erro ao listar contas:', error);
    res.status(500).json({
      status: 'error',
      message: 'Erro interno do servidor',
      error: 'Não foi possível listar as contas'
    });
  }
});

/**
 * Criar nova conta bancária
 * POST /api/accounts
 */
router.post('/', authorize('admin', 'gerente', 'caixa'), async (req, res) => {
  try {
    const {
      client_id,
      account_type,
      currency_id = 1, // AOA por padrão
      initial_deposit = 0,
      overdraft_limit = 0,
      branch_id,
      additional_holders = []
    } = req.body;

    // Validações básicas
    if (!client_id || !account_type || !branch_id) {
      return res.status(400).json({
        status: 'error',
        message: 'Dados obrigatórios não fornecidos',
        errors: {
          client_id: !client_id ? 'Cliente é obrigatório' : null,
          account_type: !account_type ? 'Tipo de conta é obrigatório' : null,
          branch_id: !branch_id ? 'Balcão é obrigatório' : null
        }
      });
    }

    // Verificar se o cliente existe
    const clientQuery = 'SELECT id, full_name, company_name, client_type FROM clients WHERE id = ? AND status = "active"';
    const clientResult = await executeQuery(clientQuery, [client_id]);

    if (clientResult.length === 0) {
      return res.status(404).json({
        status: 'error',
        message: 'Cliente não encontrado ou inativo'
      });
    }

    const client = clientResult[0];

    // Verificar se o balcão existe
    const branchQuery = 'SELECT id, name FROM branches WHERE id = ? AND is_active = 1';
    const branchResult = await executeQuery(branchQuery, [branch_id]);

    if (branchResult.length === 0) {
      return res.status(404).json({
        status: 'error',
        message: 'Balcão não encontrado ou inativo'
      });
    }

    // Gerar número de conta único
    const generateAccountNumber = () => {
      const timestamp = Date.now().toString();
      const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
      return `${timestamp.slice(-7)}${random}`;
    };

    let accountNumber;
    let attempts = 0;
    const maxAttempts = 10;

    // Garantir que o número da conta seja único
    do {
      accountNumber = generateAccountNumber();
      const existingAccount = await executeQuery(
        'SELECT id FROM accounts WHERE account_number = ?',
        [accountNumber]
      );

      if (existingAccount.length === 0) break;
      attempts++;
    } while (attempts < maxAttempts);

    if (attempts >= maxAttempts) {
      return res.status(500).json({
        status: 'error',
        message: 'Erro ao gerar número de conta único'
      });
    }

    // Criar a conta
    const accountId = `acc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const currentDate = new Date().toISOString().slice(0, 19).replace('T', ' ');

    const createAccountQuery = `
      INSERT INTO accounts (
        id, account_number, account_type, currency_id, balance, available_balance,
        overdraft_limit, status, opening_date, branch_id, created_by, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, 'active', CURDATE(), ?, ?, ?, ?)
    `;

    await executeQuery(createAccountQuery, [
      accountId,
      accountNumber,
      account_type,
      currency_id,
      initial_deposit,
      initial_deposit,
      overdraft_limit,
      branch_id,
      req.user.id,
      currentDate,
      currentDate
    ]);

    // Adicionar titular principal
    const primaryHolderId = `holder_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const addPrimaryHolderQuery = `
      INSERT INTO account_holders (id, account_id, client_id, holder_type, created_at)
      VALUES (?, ?, ?, 'primary', ?)
    `;

    await executeQuery(addPrimaryHolderQuery, [
      primaryHolderId,
      accountId,
      client_id,
      currentDate
    ]);

    // Adicionar titulares adicionais se fornecidos
    for (const holder of additional_holders) {
      if (holder.client_id && holder.holder_type === 'secondary') {
        const holderId = `holder_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        await executeQuery(addPrimaryHolderQuery, [
          holderId,
          accountId,
          holder.client_id,
          currentDate
        ]);
      }
    }

    // Se há depósito inicial, criar transação
    if (initial_deposit > 0) {
      const transactionId = `txn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const createTransactionQuery = `
        INSERT INTO transactions (
          id, account_id, transaction_type, amount, balance_after, description,
          status, created_by, created_at
        ) VALUES (?, ?, 'deposit', ?, ?, 'Depósito inicial', 'completed', ?, ?)
      `;

      await executeQuery(createTransactionQuery, [
        transactionId,
        accountId,
        initial_deposit,
        initial_deposit,
        req.user.id,
        currentDate
      ]);
    }

    // Buscar a conta criada com todos os dados
    const newAccountQuery = `
      SELECT
        a.*,
        b.name as branch_name,
        c.full_name as primary_holder_name,
        c.company_name as primary_holder_company,
        c.client_type as primary_holder_type,
        curr.code as currency_code,
        curr.symbol as currency_symbol
      FROM accounts a
      LEFT JOIN account_holders ah ON a.id = ah.account_id AND ah.holder_type = 'primary'
      LEFT JOIN clients c ON ah.client_id = c.id
      LEFT JOIN branches b ON a.branch_id = b.id
      LEFT JOIN currencies curr ON a.currency_id = curr.id
      WHERE a.id = ?
    `;

    const newAccount = await executeQuery(newAccountQuery, [accountId]);

    // Buscar todos os titulares
    const holdersQuery = `
      SELECT
        ah.*,
        c.full_name,
        c.company_name,
        c.client_type,
        c.document_type,
        c.document_number
      FROM account_holders ah
      JOIN clients c ON ah.client_id = c.id
      WHERE ah.account_id = ?
      ORDER BY ah.holder_type, ah.created_at
    `;

    newAccount[0].holders = await executeQuery(holdersQuery, [accountId]);

    logger.info(`Nova conta criada: ${accountNumber} para cliente ${client.full_name || client.company_name}`);

    res.status(201).json({
      status: 'success',
      message: 'Conta criada com sucesso',
      data: {
        account: newAccount[0]
      }
    });

  } catch (error) {
    logger.error('Erro ao criar conta:', error);
    res.status(500).json({
      status: 'error',
      message: 'Erro interno do servidor',
      error: 'Não foi possível criar a conta'
    });
  }
});

module.exports = router;
