-- <PERSON><PERSON><PERSON> da Base de Dados Twins_Bank
-- Criado em: 03/09/2025
-- Versão: 1.0.0

-- <PERSON><PERSON><PERSON>rações iniciais
SET FOREIGN_KEY_CHECKS = 0;
SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
SET AUTOCOMMIT = 0;
START TRANSACTION;
SET time_zone = "+00:00";

-- =============================================
-- TABELAS DO SISTEMA DE AUTENTICAÇÃO
-- =============================================

-- Perfis/Roles do sistema
CREATE TABLE IF NOT EXISTS `roles` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `name` VARCHAR(50) UNIQUE NOT NULL,
    `description` TEXT,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Utilizadores do sistema
CREATE TABLE IF NOT EXISTS `users` (
    `id` CHAR(36) PRIMARY KEY,
    `full_name` VARCHAR(255) NOT NULL,
    `email` VARCHAR(255) UNIQUE NOT NULL,
    `password_hash` VARCHAR(255) NOT NULL,
    `role_id` INT NOT NULL,
    `branch_id` INT NULL,
    `is_active` BOOLEAN DEFAULT TRUE,
    `last_login` TIMESTAMP NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (`role_id`) REFERENCES `roles`(`id`),
    INDEX `idx_users_email` (`email`),
    INDEX `idx_users_role` (`role_id`),
    INDEX `idx_users_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Sessões de utilizadores
CREATE TABLE IF NOT EXISTS `user_sessions` (
    `id` CHAR(36) PRIMARY KEY,
    `user_id` CHAR(36) NOT NULL,
    `token_hash` VARCHAR(255) NOT NULL,
    `expires_at` TIMESTAMP NOT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
    INDEX `idx_sessions_user` (`user_id`),
    INDEX `idx_sessions_expires` (`expires_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- TABELAS DE CONFIGURAÇÃO DO SISTEMA
-- =============================================

-- Balcões/Agências
CREATE TABLE IF NOT EXISTS `branches` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `code` VARCHAR(10) UNIQUE NOT NULL,
    `name` VARCHAR(255) NOT NULL,
    `address` TEXT,
    `phone` VARCHAR(20),
    `email` VARCHAR(255),
    `manager_id` CHAR(36) NULL,
    `is_active` BOOLEAN DEFAULT TRUE,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`manager_id`) REFERENCES `users`(`id`),
    INDEX `idx_branches_code` (`code`),
    INDEX `idx_branches_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Adicionar foreign key para branch_id em users (após criar branches)
ALTER TABLE `users` ADD FOREIGN KEY (`branch_id`) REFERENCES `branches`(`id`);

-- Moedas suportadas
CREATE TABLE IF NOT EXISTS `currencies` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `code` VARCHAR(3) UNIQUE NOT NULL,
    `name` VARCHAR(50) NOT NULL,
    `symbol` VARCHAR(5),
    `is_active` BOOLEAN DEFAULT TRUE,
    INDEX `idx_currencies_code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Taxas de câmbio
CREATE TABLE IF NOT EXISTS `exchange_rates` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `from_currency_id` INT NOT NULL,
    `to_currency_id` INT NOT NULL,
    `rate` DECIMAL(15,6) NOT NULL,
    `effective_date` DATE NOT NULL,
    `created_by` CHAR(36) NOT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`from_currency_id`) REFERENCES `currencies`(`id`),
    FOREIGN KEY (`to_currency_id`) REFERENCES `currencies`(`id`),
    FOREIGN KEY (`created_by`) REFERENCES `users`(`id`),
    INDEX `idx_exchange_rates_date` (`effective_date`),
    INDEX `idx_exchange_rates_currencies` (`from_currency_id`, `to_currency_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- TABELAS DE GESTÃO DE CLIENTES
-- =============================================

-- Clientes (particulares e empresas)
CREATE TABLE IF NOT EXISTS `clients` (
    `id` CHAR(36) PRIMARY KEY,
    `client_type` ENUM('individual', 'company') NOT NULL,
    `full_name` VARCHAR(255) NULL,
    `company_name` VARCHAR(255) NULL,
    `document_type` VARCHAR(20) NOT NULL,
    `document_number` VARCHAR(50) UNIQUE NOT NULL,
    `nif` VARCHAR(20) UNIQUE NULL,
    `birth_date` DATE NULL,
    `incorporation_date` DATE NULL,
    `nationality` VARCHAR(100),
    `gender` ENUM('M', 'F') NULL,
    `marital_status` VARCHAR(50) NULL,
    `profession` VARCHAR(100),
    `monthly_income` DECIMAL(15,2),
    `branch_id` INT NOT NULL,
    `status` ENUM('active', 'inactive', 'blocked') DEFAULT 'active',
    `created_by` CHAR(36) NOT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (`branch_id`) REFERENCES `branches`(`id`),
    FOREIGN KEY (`created_by`) REFERENCES `users`(`id`),
    INDEX `idx_clients_document` (`document_number`),
    INDEX `idx_clients_nif` (`nif`),
    INDEX `idx_clients_type` (`client_type`),
    INDEX `idx_clients_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Endereços dos clientes
CREATE TABLE IF NOT EXISTS `client_addresses` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `client_id` CHAR(36) NOT NULL,
    `address_type` ENUM('residential', 'commercial', 'correspondence') NOT NULL,
    `province` VARCHAR(100),
    `municipality` VARCHAR(100),
    `neighborhood` VARCHAR(100),
    `street` VARCHAR(255),
    `house_number` VARCHAR(20),
    `postal_code` VARCHAR(20),
    `is_primary` BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (`client_id`) REFERENCES `clients`(`id`) ON DELETE CASCADE,
    INDEX `idx_client_addresses_client` (`client_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Contactos dos clientes
CREATE TABLE IF NOT EXISTS `client_contacts` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `client_id` CHAR(36) NOT NULL,
    `contact_type` ENUM('phone_personal', 'phone_work', 'email_personal', 'email_work') NOT NULL,
    `contact_value` VARCHAR(255) NOT NULL,
    `is_primary` BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (`client_id`) REFERENCES `clients`(`id`) ON DELETE CASCADE,
    INDEX `idx_client_contacts_client` (`client_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Documentos dos clientes
CREATE TABLE IF NOT EXISTS `client_documents` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `client_id` CHAR(36) NOT NULL,
    `document_type` VARCHAR(50) NOT NULL,
    `file_path` VARCHAR(500),
    `file_name` VARCHAR(255),
    `upload_date` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `uploaded_by` CHAR(36) NOT NULL,
    FOREIGN KEY (`client_id`) REFERENCES `clients`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`uploaded_by`) REFERENCES `users`(`id`),
    INDEX `idx_client_documents_client` (`client_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- TABELAS DE CONTAS BANCÁRIAS
-- =============================================

-- Solicitações de abertura de contas (sistema de aprovação)
CREATE TABLE IF NOT EXISTS `account_applications` (
    `id` CHAR(36) PRIMARY KEY,
    `client_id` CHAR(36) NOT NULL,
    `account_type` ENUM('corrente', 'poupanca', 'salario', 'junior') NOT NULL,
    `currency_id` INT NOT NULL DEFAULT 1,
    `initial_deposit` DECIMAL(15,2) DEFAULT 0.00,
    `overdraft_limit` DECIMAL(15,2) DEFAULT 0.00,
    `branch_id` INT NOT NULL,
    `status` ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
    `rejection_reason` TEXT NULL,
    `approved_by` CHAR(36) NULL,
    `approved_at` TIMESTAMP NULL,
    `rejected_by` CHAR(36) NULL,
    `rejected_at` TIMESTAMP NULL,
    `account_id` CHAR(36) NULL, -- ID da conta criada após aprovação
    `created_by` CHAR(36) NOT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (`client_id`) REFERENCES `clients`(`id`),
    FOREIGN KEY (`currency_id`) REFERENCES `currencies`(`id`),
    FOREIGN KEY (`branch_id`) REFERENCES `branches`(`id`),
    FOREIGN KEY (`approved_by`) REFERENCES `users`(`id`),
    FOREIGN KEY (`rejected_by`) REFERENCES `users`(`id`),
    FOREIGN KEY (`account_id`) REFERENCES `accounts`(`id`),
    FOREIGN KEY (`created_by`) REFERENCES `users`(`id`),
    INDEX `idx_applications_client` (`client_id`),
    INDEX `idx_applications_status` (`status`),
    INDEX `idx_applications_branch` (`branch_id`),
    INDEX `idx_applications_created` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Contas bancárias
CREATE TABLE IF NOT EXISTS `accounts` (
    `id` CHAR(36) PRIMARY KEY,
    `account_number` VARCHAR(20) UNIQUE NOT NULL,
    `account_type` ENUM('corrente', 'poupanca', 'salario', 'junior') NOT NULL,
    `currency_id` INT NOT NULL,
    `balance` DECIMAL(15,2) DEFAULT 0.00,
    `available_balance` DECIMAL(15,2) DEFAULT 0.00,
    `overdraft_limit` DECIMAL(15,2) DEFAULT 0.00,
    `status` ENUM('active', 'inactive', 'blocked', 'closed') DEFAULT 'active',
    `opening_date` DATE NOT NULL,
    `closing_date` DATE NULL,
    `branch_id` INT NOT NULL,
    `created_by` CHAR(36) NOT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (`currency_id`) REFERENCES `currencies`(`id`),
    FOREIGN KEY (`branch_id`) REFERENCES `branches`(`id`),
    FOREIGN KEY (`created_by`) REFERENCES `users`(`id`),
    INDEX `idx_accounts_number` (`account_number`),
    INDEX `idx_accounts_type` (`account_type`),
    INDEX `idx_accounts_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Titulares das contas (relação N:N)
CREATE TABLE IF NOT EXISTS `account_holders` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `account_id` CHAR(36) NOT NULL,
    `client_id` CHAR(36) NOT NULL,
    `holder_type` ENUM('primary', 'secondary') NOT NULL,
    `signature_path` VARCHAR(500),
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`account_id`) REFERENCES `accounts`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`client_id`) REFERENCES `clients`(`id`),
    UNIQUE KEY `unique_account_client` (`account_id`, `client_id`),
    INDEX `idx_account_holders_account` (`account_id`),
    INDEX `idx_account_holders_client` (`client_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Histórico de saldos
CREATE TABLE IF NOT EXISTS `account_balances` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `account_id` CHAR(36) NOT NULL,
    `balance` DECIMAL(15,2) NOT NULL,
    `available_balance` DECIMAL(15,2) NOT NULL,
    `balance_date` DATE NOT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`account_id`) REFERENCES `accounts`(`id`) ON DELETE CASCADE,
    UNIQUE KEY `unique_account_date` (`account_id`, `balance_date`),
    INDEX `idx_account_balances_date` (`balance_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- TABELAS DE OPERAÇÕES DE CAIXA
-- =============================================

-- Caixas físicos
CREATE TABLE IF NOT EXISTS `cash_registers` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `register_number` VARCHAR(10) UNIQUE NOT NULL,
    `description` TEXT,
    `branch_id` INT NOT NULL,
    `status` ENUM('available', 'in_use', 'closed', 'maintenance') DEFAULT 'available',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`branch_id`) REFERENCES `branches`(`id`),
    INDEX `idx_cash_registers_status` (`status`),
    INDEX `idx_cash_registers_branch` (`branch_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Sessões de trabalho dos caixas
CREATE TABLE IF NOT EXISTS `cash_register_sessions` (
    `id` CHAR(36) PRIMARY KEY,
    `user_id` CHAR(36) NOT NULL,
    `cash_register_id` INT NOT NULL,
    `opening_balance` DECIMAL(15,2) NOT NULL,
    `closing_balance_declared` DECIMAL(15,2) NULL,
    `closing_balance_validated` DECIMAL(15,2) NULL,
    `status` ENUM('open', 'closed', 'validated') DEFAULT 'open',
    `opened_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `closed_at` TIMESTAMP NULL,
    `validated_at` TIMESTAMP NULL,
    `validated_by` CHAR(36) NULL,
    `notes` TEXT,
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`),
    FOREIGN KEY (`cash_register_id`) REFERENCES `cash_registers`(`id`),
    FOREIGN KEY (`validated_by`) REFERENCES `users`(`id`),
    INDEX `idx_cash_sessions_user` (`user_id`),
    INDEX `idx_cash_sessions_register` (`cash_register_id`),
    INDEX `idx_cash_sessions_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Denominações de notas/moedas
CREATE TABLE IF NOT EXISTS `cash_denominations` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `session_id` CHAR(36) NOT NULL,
    `denomination_type` ENUM('note', 'coin') NOT NULL,
    `denomination_value` DECIMAL(10,2) NOT NULL,
    `quantity` INT NOT NULL,
    `total_value` DECIMAL(15,2) NOT NULL,
    `operation_type` ENUM('opening', 'closing') NOT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`session_id`) REFERENCES `cash_register_sessions`(`id`) ON DELETE CASCADE,
    INDEX `idx_cash_denominations_session` (`session_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- CONFIGURAÇÕES E CONSTRAINTS
-- =============================================

SET FOREIGN_KEY_CHECKS = 1;
COMMIT;
