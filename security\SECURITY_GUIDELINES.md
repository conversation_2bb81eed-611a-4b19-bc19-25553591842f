# K-Bank Security Guidelines

**Versão:** 1.0  
**Data:** 08/09/2025  
**Âmbito:** Diretrizes de segurança para desenvolvimento e manutenção

---

## Princípios Fundamentais de Segurança

### 1. Defense in Depth (Defesa em Profundidade)
Implementar múltiplas camadas de segurança:
- **Perímetro:** Firewall, WAF, Rate limiting
- **Rede:** HTTPS, VPN, Segmentação
- **Aplicação:** Autenticação, autorização, validação
- **Dados:** Encriptação, backup, auditoria

### 2. Principle of Least Privilege (Princípio do Menor Privilégio)
- Utilizadores têm apenas as permissões mínimas necessárias
- Contas de serviço com privilégios limitados
- Revisão regular de permissões

### 3. Fail Secure (Falhar de Forma Segura)
- Em caso de erro, o sistema deve falhar para um estado seguro
- Logs detalhados de falhas de segurança
- Graceful degradation sem exposição de dados

### 4. Security by Design (Segurança por Design)
- Considerações de segurança desde o início do desenvolvimento
- Threat modeling para novas funcionalidades
- Security reviews obrigatórias

---

## Diretrizes de Desenvolvimento Seguro

### Autenticação e Autorização

#### ✅ FAZER
```javascript
// Usar cookies httpOnly para tokens
res.cookie('auth-token', token, {
  httpOnly: true,
  secure: true,
  sameSite: 'strict'
});

// Validar permissões em cada endpoint
router.get('/sensitive-data', 
  authenticate, 
  authorize('admin', 'manager'),
  handler
);

// Hash de senhas com salt forte
const hashedPassword = await bcrypt.hash(password, 12);
```

#### ❌ NÃO FAZER
```javascript
// NUNCA armazenar tokens em localStorage
localStorage.setItem('token', token); // VULNERÁVEL A XSS

// NUNCA confiar apenas em validação frontend
if (user.role === 'admin') { // INSEGURO
  // lógica sensível
}

// NUNCA usar hashes fracos
const hash = md5(password); // INSEGURO
```

### Validação e Sanitização de Input

#### ✅ FAZER
```javascript
// Validar com Joi
const schema = Joi.object({
  email: Joi.string().email().required(),
  amount: Joi.number().positive().max(10000).required()
});

// Sanitizar input
const sanitizedInput = validator.escape(userInput);

// Usar prepared statements
const result = await executeQuery(
  'SELECT * FROM users WHERE email = ?',
  [email]
);
```

#### ❌ NÃO FAZER
```javascript
// NUNCA concatenar strings em SQL
const query = `SELECT * FROM users WHERE id = ${userId}`; // SQL INJECTION

// NUNCA confiar em input sem validação
const amount = req.body.amount; // Pode ser qualquer coisa
processPayment(amount);

// NUNCA usar eval ou similar
eval(userInput); // EXTREMAMENTE PERIGOSO
```

### Gestão de Erros e Logging

#### ✅ FAZER
```javascript
// Logs estruturados sem dados sensíveis
logger.error('Login failed', {
  userId: user.id,
  ip: req.ip,
  timestamp: new Date().toISOString()
});

// Mensagens de erro genéricas para utilizadores
res.status(401).json({
  error: 'Credenciais inválidas'
});

// Logs detalhados para auditoria
logger.audit('Sensitive operation', {
  action: 'TRANSFER',
  userId: req.user.id,
  amount: amount,
  success: true
});
```

#### ❌ NÃO FAZER
```javascript
// NUNCA logar dados sensíveis
logger.info('User login', { password: password }); // PERIGOSO

// NUNCA expor detalhes internos
res.status(500).json({
  error: error.stack // INFORMATION DISCLOSURE
});

// NUNCA ignorar erros de segurança
try {
  validateToken(token);
} catch (e) {
  // Ignorar erro - PERIGOSO
}
```

---

## Configurações de Segurança

### Variáveis de Ambiente

#### Segredos Obrigatórios
```env
# Gerar com: node scripts/generate-secrets.js
JWT_SECRET=<64-char-hex-string>
SESSION_SECRET=<32-char-hex-string>
ENCRYPTION_KEY=<32-char-hex-string>

# Base de dados
DB_PASSWORD=<strong-password>

# Email/SMTP
SMTP_PASSWORD=<app-specific-password>
```

#### Validação de Startup
```javascript
// Validar segredos no início da aplicação
const requiredSecrets = ['JWT_SECRET', 'SESSION_SECRET'];
requiredSecrets.forEach(secret => {
  if (!process.env[secret] || process.env[secret].length < 32) {
    throw new Error(`${secret} deve ter pelo menos 32 caracteres`);
  }
});
```

### Headers de Segurança

```javascript
// Configuração completa de segurança
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      scriptSrc: ["'self'", "'nonce-{random}'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
    },
  },
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  }
}));
```

### Rate Limiting

```javascript
// Rate limiting por IP
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutos
  max: 100, // máximo 100 requests
  message: 'Muitas tentativas. Tente novamente mais tarde.',
  standardHeaders: true,
  legacyHeaders: false,
});

// Rate limiting mais restritivo para login
const loginLimiter = rateLimit({
  windowMs: 15 * 60 * 1000,
  max: 5, // máximo 5 tentativas de login
  skipSuccessfulRequests: true
});
```

---

## Checklist de Segurança

### Para Cada Nova Funcionalidade

#### Análise de Segurança
- [ ] Threat modeling realizado
- [ ] Identificação de dados sensíveis
- [ ] Análise de superfície de ataque
- [ ] Definição de controlos de segurança

#### Implementação
- [ ] Autenticação implementada
- [ ] Autorização validada
- [ ] Input validado e sanitizado
- [ ] Output encoding aplicado
- [ ] Logs de auditoria implementados

#### Testes
- [ ] Testes unitários de segurança
- [ ] Testes de penetração básicos
- [ ] Validação de controlos de acesso
- [ ] Testes de injection (SQL, XSS, etc.)

#### Deployment
- [ ] Configurações de segurança aplicadas
- [ ] Segredos geridos adequadamente
- [ ] Monitorização configurada
- [ ] Plano de resposta a incidentes

### Para Cada Release

#### Pré-Release
- [ ] Scan de vulnerabilidades executado
- [ ] Dependências atualizadas
- [ ] Configurações de segurança validadas
- [ ] Backup realizado

#### Pós-Release
- [ ] Monitorização ativa
- [ ] Logs de segurança revisados
- [ ] Métricas de segurança validadas
- [ ] Plano de rollback testado

---

## Resposta a Incidentes de Segurança

### Classificação de Incidentes

#### CRÍTICO (Resposta imediata)
- Acesso não autorizado a dados financeiros
- Comprometimento de contas de utilizadores
- Falha de autenticação/autorização
- Exfiltração de dados

#### ALTO (Resposta em 4 horas)
- Tentativas de ataque detectadas
- Vulnerabilidades críticas descobertas
- Falhas de sistema de segurança
- Anomalias de acesso

#### MÉDIO (Resposta em 24 horas)
- Vulnerabilidades médias
- Configurações incorretas
- Logs de segurança anómalos
- Falhas de monitorização

### Procedimento de Resposta

1. **Contenção Imediata**
   - Isolar sistemas afetados
   - Revogar credenciais comprometidas
   - Ativar modo de emergência se necessário

2. **Investigação**
   - Analisar logs de auditoria
   - Identificar vetor de ataque
   - Determinar extensão do comprometimento

3. **Erradicação**
   - Corrigir vulnerabilidades
   - Remover artefatos maliciosos
   - Atualizar controlos de segurança

4. **Recuperação**
   - Restaurar sistemas afetados
   - Validar integridade dos dados
   - Monitorizar atividade anómala

5. **Lições Aprendidas**
   - Documentar incidente
   - Atualizar procedimentos
   - Melhorar controlos preventivos

---

## Monitorização e Alertas

### Métricas de Segurança

#### Autenticação
- Taxa de falhas de login
- Tentativas de força bruta
- Sessões anómalas
- Acessos fora de horário

#### Aplicação
- Tentativas de injection
- Uploads maliciosos
- Acessos não autorizados
- Operações sensíveis

#### Infraestrutura
- Tráfego anómalo
- Tentativas de intrusão
- Falhas de sistema
- Configurações alteradas

### Alertas Automáticos

```javascript
// Exemplo de alerta de segurança
const securityAlert = (level, message, data) => {
  const alert = {
    level, // CRITICAL, HIGH, MEDIUM, LOW
    message,
    data,
    timestamp: new Date().toISOString(),
    source: 'k-bank-api'
  };

  // Log estruturado
  logger.security(message, alert);

  // Notificação para equipa de segurança
  if (level === 'CRITICAL' || level === 'HIGH') {
    notificationService.sendSecurityAlert(alert);
  }
};

// Uso em middleware
const detectBruteForce = (req, res, next) => {
  const attempts = getFailedAttempts(req.ip);
  if (attempts > 10) {
    securityAlert('HIGH', 'Brute force attack detected', {
      ip: req.ip,
      attempts,
      userAgent: req.get('User-Agent')
    });
  }
  next();
};
```

---

## Formação e Consciencialização

### Para Desenvolvedores

#### Tópicos Obrigatórios
- OWASP Top 10
- Secure coding practices
- Threat modeling
- Security testing

#### Recursos Recomendados
- [OWASP Developer Guide](https://owasp.org/www-project-developer-guide/)
- [SANS Secure Coding Practices](https://www.sans.org/white-papers/2172/)
- [Node.js Security Checklist](https://blog.risingstack.com/node-js-security-checklist/)

### Para Utilizadores

#### Tópicos de Consciencialização
- Gestão de senhas
- Phishing e social engineering
- Segurança de dispositivos
- Reportar incidentes

---

## Compliance e Auditoria

### Requisitos Regulamentares
- **GDPR:** Proteção de dados pessoais
- **PCI DSS:** Segurança de dados de cartões
- **ISO 27001:** Gestão de segurança da informação
- **Regulamentação Bancária:** Requisitos específicos do setor

### Auditorias Regulares
- **Mensal:** Revisão de logs de segurança
- **Trimestral:** Testes de penetração
- **Semestral:** Revisão de políticas
- **Anual:** Auditoria externa completa

---

**Documento mantido por:** Equipa de Segurança  
**Próxima revisão:** Trimestral  
**Aprovação:** CTO, CISO
