import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/hooks/use-toast';
import { Shield, Plus, Search, Edit, Trash2, Users, Loader2, AlertCircle, CheckCircle, RefreshCw } from 'lucide-react';
import ActionMenu from '@/components/ui/ActionMenu';
import { roleService, Role, CreateRoleRequest, RoleError } from '@/services/roleService';

interface FormErrors {
  name?: string;
  description?: string;
}

const RegistarRole = () => {
  const { toast } = useToast();
  
  // Estados do formulário
  const [formData, setFormData] = useState<CreateRoleRequest>({
    name: '',
    description: '',
    permissions: []
  });
  
  // Estados de controle
  const [roles, setRoles] = useState<Role[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [errors, setErrors] = useState<FormErrors>({});
  const [editingRole, setEditingRole] = useState<Role | null>(null);
  
  // Estados de filtros
  const [searchTerm, setSearchTerm] = useState('');
  const [availablePermissions, setAvailablePermissions] = useState<string[]>([]);

  // Carregar dados iniciais
  useEffect(() => {
    loadInitialData();
  }, []);

  const loadInitialData = async () => {
    setIsLoading(true);
    try {
      const [rolesData, permissionsData] = await Promise.all([
        roleService.listRoles({ limit: 100 }),
        roleService.getAvailablePermissions()
      ]);
      
      setRoles(rolesData.roles);
      setAvailablePermissions(permissionsData);
    } catch (error) {
      console.error('Erro ao carregar dados iniciais:', error);
      toast({
        title: "Erro ao carregar dados",
        description: error instanceof RoleError ? error.message : "Erro desconhecido",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Função para refresh manual
  const handleRefresh = async () => {
    setIsRefreshing(true);
    await loadInitialData();
    setIsRefreshing(false);
    toast({
      title: "Lista atualizada",
      description: "A lista de roles foi atualizada com sucesso",
    });
  };

  // Validação do formulário
  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};
    
    if (!formData.name.trim()) {
      newErrors.name = 'Nome é obrigatório';
    } else if (formData.name.length < 2) {
      newErrors.name = 'Nome deve ter pelo menos 2 caracteres';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Função para criar/atualizar role
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      toast({
        title: "Erro de validação",
        description: "Por favor, corrija os erros no formulário",
        variant: "destructive"
      });
      return;
    }

    setIsSubmitting(true);

    try {
      if (editingRole) {
        // Atualizar role existente
        await roleService.updateRole(editingRole.id, formData);
        toast({
          title: "Role atualizado",
          description: `Role ${formData.name} foi atualizado com sucesso`,
        });
      } else {
        // Criar novo role
        await roleService.createRole(formData);
        toast({
          title: "Role criado",
          description: `Role ${formData.name} foi criado com sucesso`,
        });
      }

      // Limpar formulário e recarregar lista
      resetForm();
      await loadInitialData();
    } catch (error) {
      console.error('Erro ao salvar role:', error);
      toast({
        title: editingRole ? "Erro ao atualizar role" : "Erro ao criar role",
        description: error instanceof RoleError ? error.message : "Erro desconhecido",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Função para editar role
  const handleEditRole = (role: Role) => {
    setEditingRole(role);
    setFormData({
      name: role.name,
      description: role.description,
      permissions: role.permissions || []
    });
    setErrors({});
  };

  // Função para deletar role
  const handleDeleteRole = async (role: Role) => {
    if (!confirm(`Tem certeza que deseja remover o role "${role.name}"?`)) {
      return;
    }

    try {
      await roleService.deleteRole(role.id);
      toast({
        title: "Role removido",
        description: `Role ${role.name} foi removido com sucesso`,
      });
      await loadInitialData();
    } catch (error) {
      console.error('Erro ao remover role:', error);
      toast({
        title: "Erro ao remover role",
        description: error instanceof RoleError ? error.message : "Erro desconhecido",
        variant: "destructive"
      });
    }
  };

  // Função para resetar formulário
  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      permissions: []
    });
    setEditingRole(null);
    setErrors({});
  };

  // Filtrar roles
  const filteredRoles = roles.filter(role => 
    role.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    role.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Loading state para dados iniciais
  if (isLoading) {
    return (
      <div className="max-w-6xl mx-auto space-y-6">
        <div className="flex justify-center items-center py-12">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">Carregando dados...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 content-container">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 flex items-center gap-2">
            <Shield className="h-8 w-8" />
            Gestão de Roles
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">Criar e gerir roles e permissões do sistema</p>
        </div>
        <Button onClick={handleRefresh} disabled={isRefreshing} variant="outline">
          <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
          Atualizar
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Lista de Roles */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle className="text-lg font-semibold flex items-center">
                  Lista de Roles 
                  <Badge variant="secondary" className="ml-2">
                    {filteredRoles.length}
                  </Badge>
                </CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              {/* Controles da tabela */}
              <div className="flex justify-between items-center mb-4">
                <div className="flex items-center space-x-2">
                  <Search className="h-4 w-4 text-gray-400" />
                  <Input
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    placeholder="Buscar roles..."
                    className="w-64"
                  />
                </div>
              </div>

              {/* Tabela */}
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Nome</TableHead>
                      <TableHead>Descrição</TableHead>
                      <TableHead>Usuários</TableHead>
                      <TableHead>Ações</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredRoles.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={4} className="text-center py-8 text-gray-500">
                          {searchTerm ? 'Nenhum role encontrado com os critérios de busca' : 'Nenhum role cadastrado'}
                        </TableCell>
                      </TableRow>
                    ) : (
                      filteredRoles.map((role) => (
                        <TableRow key={role.id}>
                          <TableCell className="font-medium">
                            <div className="flex items-center gap-2">
                              <Shield className="h-4 w-4 text-blue-500" />
                              {role.name}
                            </div>
                          </TableCell>
                          <TableCell className="max-w-xs truncate">
                            {role.description || '-'}
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline" className="flex items-center gap-1">
                              <Users className="h-3 w-3" />
                              {role.user_count}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <ActionMenu
                              items={[
                                {
                                  label: 'Editar',
                                  icon: Edit,
                                  onClick: () => handleEditRole(role)
                                },
                                {
                                  label: 'Excluir',
                                  icon: Trash2,
                                  onClick: () => handleDeleteRole(role),
                                  variant: 'destructive',
                                  separator: true
                                }
                              ]}
                            />
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Formulário de Role */}
        <div>
          <Card>
            <CardHeader>
              <CardTitle className="text-lg font-semibold flex items-center">
                <Plus className="h-5 w-5 mr-2" />
                {editingRole ? 'Editar Role' : 'Adicionar Role'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-4">
                {/* Nome do Role */}
                <div className="space-y-2">
                  <Label htmlFor="name">
                    Nome <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="Ex: gerente_regional"
                    className={errors.name ? 'border-red-500' : ''}
                  />
                  {errors.name && (
                    <Alert variant="destructive" className="py-2">
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription className="text-sm">
                        {errors.name}
                      </AlertDescription>
                    </Alert>
                  )}
                </div>

                {/* Descrição */}
                <div className="space-y-2">
                  <Label htmlFor="description">Descrição</Label>
                  <Input
                    id="description"
                    value={formData.description}
                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="Descrição do role"
                    className={errors.description ? 'border-red-500' : ''}
                  />
                  {errors.description && (
                    <Alert variant="destructive" className="py-2">
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription className="text-sm">
                        {errors.description}
                      </AlertDescription>
                    </Alert>
                  )}
                </div>

                {/* Botões */}
                <div className="flex gap-2 pt-4">
                  <Button 
                    type="submit"
                    disabled={isSubmitting}
                    className="flex-1"
                  >
                    {isSubmitting ? (
                      <>
                        <Loader2 className="h-4 w-4 animate-spin mr-2" />
                        {editingRole ? 'Atualizando...' : 'Criando...'}
                      </>
                    ) : (
                      <>
                        <CheckCircle className="h-4 w-4 mr-2" />
                        {editingRole ? 'Atualizar' : 'Criar Role'}
                      </>
                    )}
                  </Button>
                  
                  {editingRole && (
                    <Button 
                      type="button"
                      variant="outline" 
                      onClick={resetForm}
                      disabled={isSubmitting}
                    >
                      Cancelar
                    </Button>
                  )}
                </div>
              </form>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default RegistarRole;
