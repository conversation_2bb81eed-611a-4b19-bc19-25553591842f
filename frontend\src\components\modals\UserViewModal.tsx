import React from 'react';
import {
  Di<PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { User, Calendar, MapPin, Phone, Mail, Shield, Building2, Clock } from 'lucide-react';
import { User as UserType } from '@/services/userService';
import { formatDateTime } from '@/utils/dateUtils';

interface UserViewModalProps {
  user: UserType | null;
  isOpen: boolean;
  onClose: () => void;
}

const UserViewModal: React.FC<UserViewModalProps> = ({ user, isOpen, onClose }) => {
  if (!user) return null;

  // Função para formatar data com fallback
  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Nunca';
    return formatDateTime(dateString);
  };

  // Função para obter badge de status
  const getStatusBadge = (isActive: boolean) => {
    return (
      <Badge variant={isActive ? "default" : "secondary"}>
        {isActive ? "Ativo" : "Inativo"}
      </Badge>
    );
  };

  // Função para obter badge de role
  const getRoleBadge = (roleName: string) => {
    const roleColors: Record<string, string> = {
      admin: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",
      gerente: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
      caixa: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
      tesoureiro: "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300",
      tecnico: "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300"
    };

    const colorClass = roleColors[roleName] || "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300";

    return (
      <Badge className={colorClass}>
        {roleName}
      </Badge>
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Detalhes do Usuário
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Informações Básicas */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <User className="h-5 w-5" />
                Informações Pessoais
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Nome Completo
                  </label>
                  <p className="text-lg font-semibold">{user.full_name}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Email
                  </label>
                  <p className="flex items-center gap-2">
                    <Mail className="h-4 w-4" />
                    {user.email}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Informações Profissionais */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Informações Profissionais
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Perfil/Role
                  </label>
                  <div className="mt-1">
                    {getRoleBadge(user.role_name)}
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Status
                  </label>
                  <div className="mt-1">
                    {getStatusBadge(user.is_active)}
                  </div>
                </div>
                <div className="md:col-span-2">
                  <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Balcão
                  </label>
                  <p className="flex items-center gap-2">
                    <Building2 className="h-4 w-4" />
                    {user.branch_name || 'Não atribuído'}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Informações de Sistema */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Clock className="h-5 w-5" />
                Informações do Sistema
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Data de Criação
                  </label>
                  <p className="flex items-center gap-2">
                    <Calendar className="h-4 w-4" />
                    {formatDate(user.created_at)}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Última Atualização
                  </label>
                  <p className="flex items-center gap-2">
                    <Calendar className="h-4 w-4" />
                    {formatDate(user.updated_at)}
                  </p>
                </div>
                <div className="md:col-span-2">
                  <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Último Acesso
                  </label>
                  <p className="flex items-center gap-2">
                    <Clock className="h-4 w-4" />
                    {formatDate(user.last_login)}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* ID do Sistema */}
          <Card>
            <CardContent className="pt-6">
              <div className="text-center">
                <label className="text-xs font-medium text-gray-500 dark:text-gray-400">
                  ID do Sistema
                </label>
                <p className="text-sm font-mono text-gray-600 dark:text-gray-300">
                  {user.id}
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default UserViewModal;
