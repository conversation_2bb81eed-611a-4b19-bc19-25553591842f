const express = require('express');
const Joi = require('joi');
const { catchAsync, AppError } = require('../core/errorHandler');
const { executeQuery } = require('../config/database');
const { authorize } = require('../auth/middleware');
const logger = require('../core/logger');

const router = express.Router();

// Schema de validação para filtros de auditoria
const auditFilterSchema = Joi.object({
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(20),
  start_date: Joi.date().iso().optional(),
  end_date: Joi.date().iso().optional(),
  user_id: Joi.string().uuid().optional(),
  action: Joi.string().max(100).optional(),
  table_name: Joi.string().max(100).optional(),
  ip_address: Joi.string().ip().optional(),
  search: Joi.string().max(255).optional()
});

/**
 * GET /api/audit/users
 * Obter logs de auditoria relacionados a usuários
 */
router.get('/users', authorize('admin', 'gerente'), catchAsync(async (req, res, next) => {
  // 1. Validar parâmetros de consulta
  const { error, value } = auditFilterSchema.validate(req.query);
  if (error) {
    return next(new AppError(error.details[0].message, 400, 'VALIDATION_ERROR'));
  }

  const { page, limit, start_date, end_date, user_id, action, search } = value;
  const offset = (page - 1) * limit;

  // 2. Construir query com filtros
  let whereConditions = ["(al.table_name = 'users' OR al.action LIKE '%user%' OR al.action LIKE '%login%' OR al.action LIKE '%logout%')"];
  let queryParams = [];

  if (start_date) {
    whereConditions.push('al.created_at >= ?');
    queryParams.push(start_date);
  }

  if (end_date) {
    whereConditions.push('al.created_at <= ?');
    queryParams.push(end_date);
  }

  if (user_id) {
    whereConditions.push('al.user_id = ?');
    queryParams.push(user_id);
  }

  if (action) {
    whereConditions.push('al.action LIKE ?');
    queryParams.push(`%${action}%`);
  }

  if (search) {
    whereConditions.push('(u.full_name LIKE ? OR u.email LIKE ? OR al.action LIKE ? OR al.ip_address LIKE ?)');
    queryParams.push(`%${search}%`, `%${search}%`, `%${search}%`, `%${search}%`);
  }

  const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

  // 3. Obter logs de auditoria
  const auditLogs = await executeQuery(
    `SELECT 
       al.id,
       al.user_id,
       al.action,
       al.table_name,
       al.record_id,
       al.old_values,
       al.new_values,
       al.ip_address,
       al.user_agent,
       al.created_at,
       u.full_name as user_name,
       u.email as user_email,
       r.name as user_role
     FROM audit_logs al
     LEFT JOIN users u ON al.user_id = u.id
     LEFT JOIN roles r ON u.role_id = r.id
     ${whereClause}
     ORDER BY al.created_at DESC
     LIMIT ? OFFSET ?`,
    [...queryParams, limit, offset]
  );

  // 4. Contar total de registros
  const totalResult = await executeQuery(
    `SELECT COUNT(*) as total 
     FROM audit_logs al
     LEFT JOIN users u ON al.user_id = u.id
     ${whereClause}`,
    queryParams
  );

  const total = totalResult[0]?.total || 0;
  const totalPages = Math.ceil(total / limit);

  // 5. Formatar logs para resposta
  const formattedLogs = auditLogs.map(log => ({
    id: log.id,
    user_id: log.user_id,
    user_name: log.user_name || 'Sistema',
    user_email: log.user_email,
    user_role: log.user_role,
    action: log.action,
    table_name: log.table_name,
    record_id: log.record_id,
    old_values: log.old_values ? JSON.parse(log.old_values) : null,
    new_values: log.new_values ? JSON.parse(log.new_values) : null,
    ip_address: log.ip_address,
    user_agent: log.user_agent,
    created_at: log.created_at
  }));

  res.status(200).json({
    status: 'success',
    data: {
      logs: formattedLogs,
      pagination: {
        current_page: page,
        total_pages: totalPages,
        total_records: total,
        per_page: limit
      }
    }
  });
}));

/**
 * GET /api/audit/system
 * Obter logs de auditoria do sistema
 */
router.get('/system', authorize('admin'), catchAsync(async (req, res, next) => {
  // 1. Validar parâmetros de consulta
  const { error, value } = auditFilterSchema.validate(req.query);
  if (error) {
    return next(new AppError(error.details[0].message, 400, 'VALIDATION_ERROR'));
  }

  const { page, limit, start_date, end_date, action, table_name, search } = value;
  const offset = (page - 1) * limit;

  // 2. Construir query com filtros (excluir logs de usuários)
  let whereConditions = ["(al.table_name != 'users' OR al.table_name IS NULL)"];
  let queryParams = [];

  if (start_date) {
    whereConditions.push('al.created_at >= ?');
    queryParams.push(start_date);
  }

  if (end_date) {
    whereConditions.push('al.created_at <= ?');
    queryParams.push(end_date);
  }

  if (action) {
    whereConditions.push('al.action LIKE ?');
    queryParams.push(`%${action}%`);
  }

  if (table_name) {
    whereConditions.push('al.table_name = ?');
    queryParams.push(table_name);
  }

  if (search) {
    whereConditions.push('(al.action LIKE ? OR al.table_name LIKE ? OR al.record_id LIKE ?)');
    queryParams.push(`%${search}%`, `%${search}%`, `%${search}%`);
  }

  const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

  // 3. Obter logs de auditoria do sistema
  const auditLogs = await executeQuery(
    `SELECT 
       al.id,
       al.user_id,
       al.action,
       al.table_name,
       al.record_id,
       al.old_values,
       al.new_values,
       al.ip_address,
       al.user_agent,
       al.created_at,
       u.full_name as performed_by,
       u.email as performer_email
     FROM audit_logs al
     LEFT JOIN users u ON al.user_id = u.id
     ${whereClause}
     ORDER BY al.created_at DESC
     LIMIT ? OFFSET ?`,
    [...queryParams, limit, offset]
  );

  // 4. Contar total de registros
  const totalResult = await executeQuery(
    `SELECT COUNT(*) as total 
     FROM audit_logs al
     ${whereClause}`,
    queryParams
  );

  const total = totalResult[0]?.total || 0;
  const totalPages = Math.ceil(total / limit);

  // 5. Formatar logs para resposta
  const formattedLogs = auditLogs.map(log => ({
    id: log.id,
    performed_by: log.performed_by || 'Sistema',
    performer_email: log.performer_email,
    action: log.action,
    table_name: log.table_name,
    record_id: log.record_id,
    old_values: log.old_values ? JSON.parse(log.old_values) : null,
    new_values: log.new_values ? JSON.parse(log.new_values) : null,
    ip_address: log.ip_address,
    user_agent: log.user_agent,
    created_at: log.created_at
  }));

  res.status(200).json({
    status: 'success',
    data: {
      logs: formattedLogs,
      pagination: {
        current_page: page,
        total_pages: totalPages,
        total_records: total,
        per_page: limit
      }
    }
  });
}));

/**
 * GET /api/audit/security
 * Obter logs de auditoria de segurança (logins, logouts, tentativas de acesso)
 */
router.get('/security', authorize('admin'), catchAsync(async (req, res, next) => {
  // 1. Validar parâmetros de consulta
  const { error, value } = auditFilterSchema.validate(req.query);
  if (error) {
    return next(new AppError(error.details[0].message, 400, 'VALIDATION_ERROR'));
  }

  const { page, limit, start_date, end_date, user_id, ip_address, search } = value;
  const offset = (page - 1) * limit;

  // 2. Construir query com filtros (apenas logs de segurança)
  let whereConditions = ["(al.action LIKE '%login%' OR al.action LIKE '%logout%' OR al.action LIKE '%auth%' OR al.action LIKE '%access%' OR al.action LIKE '%permission%')"];
  let queryParams = [];

  if (start_date) {
    whereConditions.push('al.created_at >= ?');
    queryParams.push(start_date);
  }

  if (end_date) {
    whereConditions.push('al.created_at <= ?');
    queryParams.push(end_date);
  }

  if (user_id) {
    whereConditions.push('al.user_id = ?');
    queryParams.push(user_id);
  }

  if (ip_address) {
    whereConditions.push('al.ip_address = ?');
    queryParams.push(ip_address);
  }

  if (search) {
    whereConditions.push('(u.full_name LIKE ? OR u.email LIKE ? OR al.action LIKE ? OR al.ip_address LIKE ?)');
    queryParams.push(`%${search}%`, `%${search}%`, `%${search}%`, `%${search}%`);
  }

  const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

  // 3. Obter logs de segurança
  const auditLogs = await executeQuery(
    `SELECT
       al.id,
       al.user_id,
       al.action,
       al.table_name,
       al.record_id,
       al.old_values,
       al.new_values,
       al.ip_address,
       al.user_agent,
       al.created_at,
       u.full_name as user_name,
       u.email as user_email,
       r.name as user_role
     FROM audit_logs al
     LEFT JOIN users u ON al.user_id = u.id
     LEFT JOIN roles r ON u.role_id = r.id
     ${whereClause}
     ORDER BY al.created_at DESC
     LIMIT ? OFFSET ?`,
    [...queryParams, limit, offset]
  );

  // 4. Contar total de registros
  const totalResult = await executeQuery(
    `SELECT COUNT(*) as total
     FROM audit_logs al
     LEFT JOIN users u ON al.user_id = u.id
     ${whereClause}`,
    queryParams
  );

  const total = totalResult[0]?.total || 0;
  const totalPages = Math.ceil(total / limit);

  // 5. Formatar logs para resposta
  const formattedLogs = auditLogs.map(log => ({
    id: log.id,
    user_id: log.user_id,
    user_name: log.user_name || 'Sistema',
    user_email: log.user_email,
    user_role: log.user_role,
    action: log.action,
    table_name: log.table_name,
    record_id: log.record_id,
    old_values: log.old_values ? JSON.parse(log.old_values) : null,
    new_values: log.new_values ? JSON.parse(log.new_values) : null,
    ip_address: log.ip_address,
    user_agent: log.user_agent,
    created_at: log.created_at,
    risk_level: determineRiskLevel(log.action, log.ip_address)
  }));

  res.status(200).json({
    status: 'success',
    data: {
      logs: formattedLogs,
      pagination: {
        current_page: page,
        total_pages: totalPages,
        total_records: total,
        per_page: limit
      }
    }
  });
}));

/**
 * GET /api/audit/summary
 * Obter resumo estatístico dos logs de auditoria
 */
router.get('/summary', authorize('admin', 'gerente'), catchAsync(async (req, res, next) => {
  const { start_date, end_date } = req.query;

  let dateFilter = '';
  let queryParams = [];

  if (start_date && end_date) {
    dateFilter = 'WHERE created_at BETWEEN ? AND ?';
    queryParams = [start_date, end_date];
  }

  // 1. Estatísticas gerais
  const generalStats = await executeQuery(
    `SELECT
       COUNT(*) as total_logs,
       COUNT(DISTINCT user_id) as unique_users,
       COUNT(DISTINCT ip_address) as unique_ips,
       COUNT(DISTINCT DATE(created_at)) as active_days
     FROM audit_logs ${dateFilter}`,
    queryParams
  );

  // 2. Top ações
  const topActions = await executeQuery(
    `SELECT action, COUNT(*) as count
     FROM audit_logs ${dateFilter}
     GROUP BY action
     ORDER BY count DESC
     LIMIT 10`,
    queryParams
  );

  // 3. Top usuários
  const topUsers = await executeQuery(
    `SELECT
       u.full_name,
       u.email,
       COUNT(al.id) as log_count
     FROM audit_logs al
     LEFT JOIN users u ON al.user_id = u.id
     ${dateFilter}
     GROUP BY al.user_id, u.full_name, u.email
     ORDER BY log_count DESC
     LIMIT 10`,
    queryParams
  );

  // 4. Atividade por hora
  const hourlyActivity = await executeQuery(
    `SELECT
       HOUR(created_at) as hour,
       COUNT(*) as count
     FROM audit_logs ${dateFilter}
     GROUP BY HOUR(created_at)
     ORDER BY hour`,
    queryParams
  );

  res.status(200).json({
    status: 'success',
    data: {
      general_stats: generalStats[0],
      top_actions: topActions,
      top_users: topUsers,
      hourly_activity: hourlyActivity
    }
  });
}));

/**
 * Determinar nível de risco de uma ação de segurança
 */
function determineRiskLevel(action, ipAddress) {
  if (!action) return 'low';

  const actionLower = action.toLowerCase();

  if (actionLower.includes('failed') || actionLower.includes('denied') || actionLower.includes('blocked')) {
    return 'high';
  }

  if (actionLower.includes('login') || actionLower.includes('access') || actionLower.includes('permission')) {
    return 'medium';
  }

  return 'low';
}

module.exports = router;
