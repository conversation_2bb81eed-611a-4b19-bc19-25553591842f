const express = require('express');
const Joi = require('joi');
const { v4: uuidv4 } = require('uuid');
const path = require('path');
const fs = require('fs');
const { catchAsync, AppError } = require('../core/errorHandler');
const { executeQuery, executeTransaction } = require('../config/database');
const { hashPassword } = require('../auth/jwtUtils');
const { authorize } = require('../auth/middleware');
const logger = require('../core/logger');
const { avatarUpload, deleteOldFile, getAvatarUrl } = require('../core/uploadConfig');

const router = express.Router();

// Validação de esquemas
const createUserSchema = Joi.object({
  full_name: Joi.string().min(2).max(255).required().messages({
    'string.min': 'Nome deve ter pelo menos 2 caracteres',
    'string.max': 'Nome deve ter no máximo 255 caracteres',
    'any.required': 'Nome completo é obrigatório'
  }),
  email: Joi.string().email().required().messages({
    'string.email': 'Email deve ter um formato válido',
    'any.required': 'Email é obrigatório'
  }),
  password: Joi.string().min(8).required().messages({
    'string.min': 'Senha deve ter pelo menos 8 caracteres',
    'any.required': 'Senha é obrigatória'
  }),
  role_id: Joi.number().integer().min(1).required().messages({
    'number.base': 'Role ID deve ser um número',
    'number.integer': 'Role ID deve ser um número inteiro',
    'number.min': 'Role ID deve ser maior que 0',
    'any.required': 'Role é obrigatório'
  }),
  branch_id: Joi.number().integer().min(1).optional().messages({
    'number.base': 'Branch ID deve ser um número',
    'number.integer': 'Branch ID deve ser um número inteiro',
    'number.min': 'Branch ID deve ser maior que 0'
  })
});

const updateUserSchema = Joi.object({
  full_name: Joi.string().min(2).max(255).optional(),
  email: Joi.string().email().optional(),
  role_id: Joi.number().integer().min(1).optional(),
  branch_id: Joi.number().integer().min(1).optional().allow(null),
  is_active: Joi.boolean().optional()
});

/**
 * GET /api/users
 * Listar utilizadores (apenas Admin e Gerente)
 */
router.get('/', authorize('admin', 'gerente'), catchAsync(async (req, res) => {
  const { page = 1, limit = 10, search = '', role = '', branch = '', active = '' } = req.query;
  
  const offset = (page - 1) * limit;
  
  // Construir query com filtros
  let whereConditions = [];
  let queryParams = [];
  
  if (search) {
    whereConditions.push('(u.full_name LIKE ? OR u.email LIKE ?)');
    queryParams.push(`%${search}%`, `%${search}%`);
  }
  
  if (role) {
    whereConditions.push('r.name = ?');
    queryParams.push(role);
  }
  
  if (branch) {
    whereConditions.push('u.branch_id = ?');
    queryParams.push(branch);
  }
  
  if (active !== '') {
    whereConditions.push('u.is_active = ?');
    queryParams.push(active === 'true');
  }
  
  const whereClause = whereConditions.length > 0 ? 'WHERE ' + whereConditions.join(' AND ') : '';
  
  // Query principal
  const users = await executeQuery(
    `SELECT u.id, u.full_name, u.email, u.avatar_url, u.is_active, u.last_login, u.created_at,
            r.name as role_name, b.name as branch_name, b.code as branch_code
     FROM users u
     JOIN roles r ON u.role_id = r.id
     LEFT JOIN branches b ON u.branch_id = b.id
     ${whereClause}
     ORDER BY u.created_at DESC
     LIMIT ? OFFSET ?`,
    [...queryParams, parseInt(limit), offset]
  );
  
  // Contar total
  const totalResult = await executeQuery(
    `SELECT COUNT(*) as total 
     FROM users u 
     JOIN roles r ON u.role_id = r.id 
     LEFT JOIN branches b ON u.branch_id = b.id 
     ${whereClause}`,
    queryParams
  );
  
  const total = totalResult[0].total;
  const totalPages = Math.ceil(total / limit);
  
  res.status(200).json({
    status: 'success',
    data: {
      users,
      pagination: {
        current_page: parseInt(page),
        total_pages: totalPages,
        total_records: total,
        per_page: parseInt(limit)
      }
    }
  });
}));

/**
 * GET /api/users/:id
 * Obter utilizador específico
 */
router.get('/:id', authorize('admin', 'gerente'), catchAsync(async (req, res, next) => {
  const { id } = req.params;
  
  const users = await executeQuery(
    `SELECT u.id, u.full_name, u.email, u.avatar_url, u.role_id, u.branch_id, u.is_active,
            u.last_login, u.created_at, u.updated_at,
            r.name as role_name, r.description as role_description,
            b.name as branch_name, b.code as branch_code
     FROM users u
     JOIN roles r ON u.role_id = r.id
     LEFT JOIN branches b ON u.branch_id = b.id
     WHERE u.id = ?`,
    [id]
  );
  
  if (!users || users.length === 0) {
    return next(new AppError('Utilizador não encontrado', 404, 'USER_NOT_FOUND'));
  }
  
  res.status(200).json({
    status: 'success',
    data: {
      user: users[0]
    }
  });
}));

/**
 * POST /api/users
 * Criar novo utilizador (apenas Admin)
 */
router.post('/', authorize('admin'), catchAsync(async (req, res, next) => {
  // 1. Validar dados de entrada
  const { error, value } = createUserSchema.validate(req.body);
  if (error) {
    return next(new AppError(error.details[0].message, 400, 'VALIDATION_ERROR'));
  }
  
  const { full_name, email, password, role_id, branch_id } = value;
  
  // 2. Verificar se email já existe
  const existingUsers = await executeQuery(
    'SELECT id FROM users WHERE email = ?',
    [email]
  );
  
  if (existingUsers && existingUsers.length > 0) {
    return next(new AppError('Email já está em uso', 409, 'EMAIL_EXISTS'));
  }
  
  // 3. Verificar se role existe
  const roles = await executeQuery('SELECT id FROM roles WHERE id = ?', [role_id]);
  if (!roles || roles.length === 0) {
    return next(new AppError('Role inválido', 400, 'INVALID_ROLE'));
  }
  
  // 4. Verificar se branch existe (se fornecido)
  if (branch_id) {
    const branches = await executeQuery('SELECT id FROM branches WHERE id = ?', [branch_id]);
    if (!branches || branches.length === 0) {
      return next(new AppError('Balcão inválido', 400, 'INVALID_BRANCH'));
    }
  }
  
  // 5. Hash da senha
  const passwordHash = await hashPassword(password);
  
  // 6. Criar utilizador
  const userId = uuidv4();
  
  await executeQuery(
    `INSERT INTO users (id, full_name, email, password_hash, role_id, branch_id, is_active) 
     VALUES (?, ?, ?, ?, ?, ?, ?)`,
    [userId, full_name, email, passwordHash, role_id, branch_id || null, true]
  );
  
  // 7. Obter dados completos do utilizador criado
  const newUsers = await executeQuery(
    `SELECT u.id, u.full_name, u.email, u.avatar_url, u.is_active, u.created_at,
            r.name as role_name, b.name as branch_name
     FROM users u
     JOIN roles r ON u.role_id = r.id
     LEFT JOIN branches b ON u.branch_id = b.id
     WHERE u.id = ?`,
    [userId]
  );
  
  logger.info(`Novo utilizador criado: ${email}`, { 
    userId, 
    createdBy: req.user.id,
    role: role_id 
  });
  
  res.status(201).json({
    status: 'success',
    message: 'Utilizador criado com sucesso',
    data: {
      user: newUsers[0]
    }
  });
}));

/**
 * PUT /api/users/:id
 * Atualizar utilizador (apenas Admin)
 */
router.put('/:id', authorize('admin'), catchAsync(async (req, res, next) => {
  const { id } = req.params;
  
  // 1. Validar dados de entrada
  const { error, value } = updateUserSchema.validate(req.body);
  if (error) {
    return next(new AppError(error.details[0].message, 400, 'VALIDATION_ERROR'));
  }
  
  // 2. Verificar se utilizador existe
  const existingUsers = await executeQuery('SELECT id FROM users WHERE id = ?', [id]);
  if (!existingUsers || existingUsers.length === 0) {
    return next(new AppError('Utilizador não encontrado', 404, 'USER_NOT_FOUND'));
  }
  
  // 3. Construir query de atualização
  const updateFields = [];
  const updateValues = [];
  
  Object.keys(value).forEach(key => {
    if (value[key] !== undefined) {
      updateFields.push(`${key} = ?`);
      updateValues.push(value[key]);
    }
  });
  
  if (updateFields.length === 0) {
    return next(new AppError('Nenhum campo para atualizar', 400, 'NO_FIELDS_TO_UPDATE'));
  }
  
  updateValues.push(id);
  
  // 4. Atualizar utilizador
  await executeQuery(
    `UPDATE users SET ${updateFields.join(', ')}, updated_at = NOW() WHERE id = ?`,
    updateValues
  );
  
  // 5. Obter dados atualizados
  const updatedUsers = await executeQuery(
    `SELECT u.id, u.full_name, u.email, u.avatar_url, u.is_active, u.updated_at,
            r.name as role_name, b.name as branch_name
     FROM users u
     JOIN roles r ON u.role_id = r.id
     LEFT JOIN branches b ON u.branch_id = b.id
     WHERE u.id = ?`,
    [id]
  );
  
  logger.info(`Utilizador atualizado: ${id}`, { 
    updatedBy: req.user.id,
    fields: Object.keys(value)
  });
  
  res.status(200).json({
    status: 'success',
    message: 'Utilizador atualizado com sucesso',
    data: {
      user: updatedUsers[0]
    }
  });
}));

/**
 * PUT /api/users/profile
 * Atualizar perfil próprio (qualquer utilizador autenticado)
 */
router.put('/profile', catchAsync(async (req, res, next) => {
  const userId = req.user.id;

  // Schema de validação para perfil próprio
  const profileSchema = Joi.object({
    full_name: Joi.string().min(2).max(255).optional(),
    email: Joi.string().email().optional()
  });

  // 1. Validar dados de entrada
  const { error, value } = profileSchema.validate(req.body);
  if (error) {
    return next(new AppError(error.details[0].message, 400, 'VALIDATION_ERROR'));
  }

  // 2. Verificar se email já existe (se fornecido)
  if (value.email) {
    const existingUsers = await executeQuery(
      'SELECT id FROM users WHERE email = ? AND id != ?',
      [value.email, userId]
    );

    if (existingUsers && existingUsers.length > 0) {
      return next(new AppError('Email já está em uso', 409, 'EMAIL_EXISTS'));
    }
  }

  // 3. Construir query de atualização
  const updateFields = [];
  const updateValues = [];

  Object.keys(value).forEach(key => {
    if (value[key] !== undefined) {
      updateFields.push(`${key} = ?`);
      updateValues.push(value[key]);
    }
  });

  if (updateFields.length === 0) {
    return next(new AppError('Nenhum campo para atualizar', 400, 'NO_FIELDS_TO_UPDATE'));
  }

  updateValues.push(userId);

  // 4. Atualizar perfil
  await executeQuery(
    `UPDATE users SET ${updateFields.join(', ')}, updated_at = NOW() WHERE id = ?`,
    updateValues
  );

  // 5. Obter dados atualizados
  const updatedUsers = await executeQuery(
    `SELECT u.id, u.full_name, u.email, u.avatar_url, u.is_active, u.updated_at,
            r.name as role_name, b.name as branch_name
     FROM users u
     JOIN roles r ON u.role_id = r.id
     LEFT JOIN branches b ON u.branch_id = b.id
     WHERE u.id = ?`,
    [userId]
  );

  logger.info(`Perfil atualizado pelo próprio utilizador: ${userId}`, {
    fields: Object.keys(value)
  });

  res.status(200).json({
    status: 'success',
    message: 'Perfil atualizado com sucesso',
    data: {
      user: updatedUsers[0]
    }
  });
}));

/**
 * POST /api/users/avatar
 * Upload de avatar (qualquer utilizador autenticado)
 */
router.post('/avatar', avatarUpload.single('avatar'), catchAsync(async (req, res, next) => {
  const userId = req.user.id;

  if (!req.file) {
    return next(new AppError('Nenhum arquivo foi enviado', 400, 'NO_FILE'));
  }

  // 1. Obter avatar atual para deletar depois
  const currentUsers = await executeQuery(
    'SELECT avatar_url FROM users WHERE id = ?',
    [userId]
  );

  const currentAvatarUrl = currentUsers[0]?.avatar_url;

  // 2. Gerar URL do novo avatar
  const avatarUrl = getAvatarUrl(req.file.filename);

  // 3. Atualizar avatar no banco de dados
  await executeQuery(
    'UPDATE users SET avatar_url = ?, updated_at = NOW() WHERE id = ?',
    [avatarUrl, userId]
  );

  // 4. Deletar avatar antigo se existir
  if (currentAvatarUrl) {
    const oldFilePath = path.join('backend', currentAvatarUrl);
    deleteOldFile(oldFilePath);
  }

  logger.info(`Avatar atualizado para utilizador: ${userId}`, {
    filename: req.file.filename,
    size: req.file.size
  });

  res.status(200).json({
    status: 'success',
    message: 'Avatar atualizado com sucesso',
    data: {
      avatar_url: avatarUrl,
      filename: req.file.filename
    }
  });
}));

/**
 * GET /api/users/:id/activity
 * Obter histórico de atividades do utilizador
 */
router.get('/:id/activity', authorize('admin', 'gerente'), catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const { page = 1, limit = 20 } = req.query;

  const offset = (page - 1) * limit;

  // 1. Verificar se utilizador existe
  const users = await executeQuery('SELECT id, full_name FROM users WHERE id = ?', [id]);
  if (!users || users.length === 0) {
    return next(new AppError('Utilizador não encontrado', 404, 'USER_NOT_FOUND'));
  }

  // 2. Obter atividades do audit_logs
  const activities = await executeQuery(
    `SELECT
       al.action,
       al.table_name,
       al.record_id,
       al.old_values,
       al.new_values,
       al.ip_address,
       al.created_at,
       u.full_name as performed_by
     FROM audit_logs al
     LEFT JOIN users u ON al.user_id = u.id
     WHERE al.user_id = ? OR al.record_id = ?
     ORDER BY al.created_at DESC
     LIMIT ? OFFSET ?`,
    [id, id, parseInt(limit), parseInt(offset)]
  );

  // 3. Contar total de atividades
  const totalResult = await executeQuery(
    `SELECT COUNT(*) as total
     FROM audit_logs
     WHERE user_id = ? OR record_id = ?`,
    [id, id]
  );

  const total = totalResult[0]?.total || 0;
  const totalPages = Math.ceil(total / limit);

  // 4. Formatar atividades para resposta
  const formattedActivities = activities.map(activity => ({
    action: activity.action,
    table_name: activity.table_name,
    record_id: activity.record_id,
    old_values: activity.old_values ? JSON.parse(activity.old_values) : null,
    new_values: activity.new_values ? JSON.parse(activity.new_values) : null,
    ip_address: activity.ip_address,
    performed_by: activity.performed_by || 'Sistema',
    created_at: activity.created_at
  }));

  res.status(200).json({
    status: 'success',
    data: {
      user: users[0],
      activities: formattedActivities,
      pagination: {
        current_page: parseInt(page),
        total_pages: totalPages,
        total_records: total,
        per_page: parseInt(limit)
      }
    }
  });
}));

module.exports = router;
