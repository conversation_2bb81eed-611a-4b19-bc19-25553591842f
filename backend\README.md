# K-Bank Backend API

Sistema bancário completo desenvolvido em Node.js com Express, MySQL e autenticação JWT.

## 🏗️ Arquitetura

O backend segue uma arquitetura modular (feature-sliced) com separação clara de responsabilidades:

```
backend/
├── src/
│   ├── auth/           # Autenticação e autorização
│   ├── users/          # Gestão de utilizadores
│   ├── clients/        # Gestão de clientes
│   ├── accounts/       # Contas bancárias
│   ├── cash-register/  # Operações de caixa
│   ├── treasury/       # Operações de tesouraria
│   ├── transfers/      # Transferências
│   ├── cards/          # Gestão de cartões
│   ├── exchange/       # Operações de câmbio
│   ├── insurance/      # Gestão de seguros
│   ├── atm/           # Gestão de ATMs
│   ├── reports/       # Relatórios
│   ├── core/          # Middleware e utilitários
│   ├── config/        # Configurações
│   ├── database/      # Schema e seeds
│   └── routes/        # Definição de rotas
├── tests/             # Testes
├── docs/              # Documentação
└── scripts/           # Scripts de deployment
```

## 🚀 Instalação e Configuração

### Pré-requisitos
- Node.js 18+
- MySQL 8.0+
- npm ou yarn

### 1. Instalar dependências
```bash
cd backend
npm install
```

### 2. Configurar variáveis de ambiente
```bash
cp .env.example .env
```

Edite o ficheiro `.env` com as suas configurações:
```env
# Base de Dados
DB_HOST=localhost
DB_PORT=3306
DB_NAME=twins_bank
DB_USER=seu_usuario
DB_PASSWORD=sua_senha

# JWT
JWT_SECRET=sua_chave_secreta_jwt
JWT_EXPIRES_IN=24h

# Servidor
PORT=3001
NODE_ENV=development
```

### 3. Configurar base de dados
```bash
# Criar base de dados
mysql -u root -p -e "CREATE DATABASE twins_bank;"

# Executar schema
mysql -u root -p twins_bank < src/database/schema.sql

# Inserir dados iniciais
mysql -u root -p twins_bank < src/database/seeds.sql
```

### 4. Iniciar servidor
```bash
# Desenvolvimento
npm run dev

# Produção
npm start
```

## 🔐 Autenticação

O sistema utiliza JWT (JSON Web Tokens) para autenticação com os seguintes endpoints:

### Login
```http
POST /api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "*********"
}
```

### Resposta de sucesso
```json
{
  "status": "success",
  "message": "Login realizado com sucesso",
  "data": {
    "user": {
      "id": "550e8400-e29b-41d4-a716-************",
      "full_name": "Super Administrador",
      "email": "<EMAIL>",
      "role": "admin"
    },
    "tokens": {
      "accessToken": "eyJhbGciOiJIUzI1NiIs...",
      "refreshToken": "eyJhbGciOiJIUzI1NiIs...",
      "expiresIn": "24h"
    }
  }
}
```

### Utilizar token nas requisições
```http
Authorization: Bearer eyJhbGciOiJIUzI1NiIs...
```

## 👥 Roles e Permissões

O sistema possui 5 roles principais:

1. **Admin** - Acesso total ao sistema
2. **Gerente** - Gestão operacional e relatórios
3. **Tesoureiro** - Gestão do cofre e operações de tesouraria
4. **Caixa** - Operações de atendimento ao cliente
5. **Técnico** - Suporte técnico e manutenção

## 📊 Endpoints Principais

### Autenticação
- `POST /api/auth/login` - Login
- `POST /api/auth/logout` - Logout
- `POST /api/auth/refresh` - Renovar token
- `GET /api/auth/me` - Dados do utilizador

### Utilizadores
- `GET /api/users` - Listar utilizadores
- `POST /api/users` - Criar utilizador
- `GET /api/users/:id` - Obter utilizador
- `PUT /api/users/:id` - Atualizar utilizador

### Caixa
- `GET /api/cash-registers/available` - Caixas disponíveis
- `POST /api/cash-registers/sessions/open` - Abrir sessão
- `POST /api/cash-registers/sessions/close` - Fechar sessão

### Health Check
- `GET /api/health` - Status do sistema

## 🗄️ Base de Dados

### Principais Tabelas
- **users** - Utilizadores do sistema
- **roles** - Perfis de acesso
- **clients** - Clientes do banco
- **accounts** - Contas bancárias
- **transactions** - Transações
- **cash_registers** - Caixas físicos
- **cash_register_sessions** - Sessões de trabalho

### Utilizadores de Teste
- **Super Admin**: <EMAIL> / *********
- **Gerente**: <EMAIL> / *********
- **Tesoureiro**: <EMAIL> / *********
- **Caixa**: <EMAIL> / *********
- **Técnico**: <EMAIL> / *********

## 🧪 Testes

```bash
# Executar todos os testes
npm test

# Executar testes em modo watch
npm run test:watch

# Gerar relatório de cobertura
npm run test:coverage
```

## 📝 Logs

Os logs são armazenados na pasta `logs/`:
- `app.log` - Todos os logs
- `error.log` - Apenas erros

Níveis de log disponíveis: error, warn, info, http, debug

## 🔒 Segurança

- **Helmet** - Headers de segurança
- **CORS** - Controlo de origem cruzada
- **Rate Limiting** - Limite de requisições
- **bcrypt** - Hash de senhas
- **JWT** - Tokens seguros
- **Joi** - Validação de entrada

## 🚀 Deployment

```bash
# Build para produção
npm run build

# Executar migrações
npm run migrate

# Iniciar em produção
NODE_ENV=production npm start
```

## 📚 Documentação da API

A documentação completa da API estará disponível em `/api/docs` quando o Swagger for configurado.

## 🤝 Contribuição

1. Fork o projeto
2. Crie uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

## 📄 Licença

Este projeto está licenciado sob a Licença MIT - veja o arquivo [LICENSE](LICENSE) para detalhes.
