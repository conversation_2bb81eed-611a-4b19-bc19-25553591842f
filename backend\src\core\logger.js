const winston = require('winston');
const path = require('path');

// Configuração dos níveis de log
const logLevels = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  debug: 4
};

// Configuração das cores para cada nível
const logColors = {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  http: 'magenta',
  debug: 'white'
};

winston.addColors(logColors);

// Formato personalizado para logs
const logFormat = winston.format.combine(
  winston.format.timestamp({ format: 'DD-MM-YYYY HH:mm:ss:ms' }),
  winston.format.colorize({ all: true }),
  winston.format.printf(
    (info) => `${info.timestamp} ${info.level}: ${info.message}`
  )
);

// Formato para ficheiros (sem cores)
const fileLogFormat = winston.format.combine(
  winston.format.timestamp({ format: 'DD-MM-YYYY HH:mm:ss:ms' }),
  winston.format.json()
);

// Configuração dos transportes
const transports = [
  // Console transport
  new winston.transports.Console({
    format: logFormat,
    level: process.env.LOG_LEVEL || 'info'
  }),
  
  // Ficheiro para todos os logs
  new winston.transports.File({
    filename: path.join(__dirname, '../../logs/app.log'),
    format: fileLogFormat,
    level: 'info',
    maxsize: 5242880, // 5MB
    maxFiles: 5
  }),
  
  // Ficheiro apenas para erros
  new winston.transports.File({
    filename: path.join(__dirname, '../../logs/error.log'),
    format: fileLogFormat,
    level: 'error',
    maxsize: 5242880, // 5MB
    maxFiles: 5
  })
];

// Criar logger
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  levels: logLevels,
  format: fileLogFormat,
  transports,
  exitOnError: false
});

// Função para log de requests HTTP
logger.http = (message, meta = {}) => {
  logger.log('http', message, meta);
};

// Função para log de base de dados
logger.database = (message, meta = {}) => {
  logger.info(`[DATABASE] ${message}`, meta);
};

// Função para log de autenticação
logger.auth = (message, meta = {}) => {
  logger.info(`[AUTH] ${message}`, meta);
};

// Função para log de transações
logger.transaction = (message, meta = {}) => {
  logger.info(`[TRANSACTION] ${message}`, meta);
};

// Função para log de segurança
logger.security = (message, meta = {}) => {
  logger.warn(`[SECURITY] ${message}`, meta);
};

// Criar directório de logs se não existir
const fs = require('fs');
const logsDir = path.join(__dirname, '../../logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

module.exports = logger;
