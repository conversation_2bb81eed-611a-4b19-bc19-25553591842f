const express = require('express');
const { catchAsync, AppError } = require('../core/errorHandler');
const { executeQuery } = require('../config/database');
const { authorize } = require('../auth/middleware');

const router = express.Router();

/**
 * GET /api/cash-registers/available
 * Listar caixas disponíveis para seleção
 */
router.get('/available', authorize('admin', 'gerente', 'caixa'), catchAsync(async (req, res) => {
  // Obter caixas disponíveis
  let query = `SELECT cr.id, cr.register_number, cr.description, cr.status,
               b.name as branch_name, b.code as branch_code
               FROM cash_registers cr
               JOIN branches b ON cr.branch_id = b.id
               WHERE cr.status = 'available'`;

  let params = [];

  // Se não for admin, filtrar por balcão do utilizador
  if (req.user.role_name !== 'admin') {
    query += ' AND cr.branch_id = ?';
    params.push(req.user.branch_id);
  }

  query += ' ORDER BY cr.register_number';

  const availableCashRegisters = await executeQuery(query, params);

  res.status(200).json({
    status: 'success',
    message: 'Caixas disponíveis obtidos com sucesso',
    data: {
      cash_registers: availableCashRegisters
    }
  });
}));

/**
 * POST /api/cash-registers/sessions/open
 * Abrir sessão de caixa
 */
router.post('/sessions/open', authorize('caixa'), (req, res) => {
  res.status(201).json({
    status: 'success',
    message: 'Abertura de sessão de caixa - Em desenvolvimento'
  });
});

/**
 * POST /api/cash-registers/sessions/close
 * Fechar sessão de caixa
 */
router.post('/sessions/close', authorize('caixa'), (req, res) => {
  res.status(200).json({
    status: 'success',
    message: 'Fecho de sessão de caixa - Em desenvolvimento'
  });
});

/**
 * GET /api/cash-registers/sessions
 * Listar sessões de caixa
 */
router.get('/sessions', authorize('admin', 'gerente', 'tesoureiro'), (req, res) => {
  res.status(200).json({
    status: 'success',
    message: 'Listagem de sessões - Em desenvolvimento',
    data: []
  });
});

module.exports = router;
