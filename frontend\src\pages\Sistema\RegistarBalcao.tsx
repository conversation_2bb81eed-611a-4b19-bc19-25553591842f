
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/hooks/use-toast';
import { Pencil, Trash2, Plus, Search, Loader2, AlertCircle, CheckCircle, Eye, EyeOff } from 'lucide-react';
import ActionMenu from '@/components/ui/ActionMenu';
import { branchService, Branch, CreateBranchRequest, BranchError } from '@/services/branchService';

interface FormErrors {
  code?: string;
  name?: string;
  address?: string;
  phone?: string;
  email?: string;
}

const RegistarBalcao = () => {
  const { toast } = useToast();

  // Estados do formulário
  const [formData, setFormData] = useState<CreateBranchRequest>({
    code: '',
    name: '',
    address: '',
    phone: '',
    email: ''
  });

  // Estados de controle
  const [branches, setBranches] = useState<Branch[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<FormErrors>({});
  const [editingBranch, setEditingBranch] = useState<Branch | null>(null);

  // Estados de filtros e paginação
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');
  const [totalBranches, setTotalBranches] = useState(0);
  const [totalPages, setTotalPages] = useState(0);

  // Carregar balcões ao montar o componente
  useEffect(() => {
    loadBranches();
  }, [currentPage, itemsPerPage, searchTerm]);

  // Gerar código automaticamente quando não estiver editando
  useEffect(() => {
    if (!editingBranch && branches.length > 0) {
      generateNextCode();
    }
  }, [branches, editingBranch]);

  // Função para carregar balcões
  const loadBranches = async () => {
    setIsLoading(true);
    try {
      const response = await branchService.listBranches({
        page: currentPage,
        limit: itemsPerPage,
        search: searchTerm || undefined
      });

      setBranches(response.branches);
      setTotalBranches(response.pagination.total);
      setTotalPages(response.pagination.totalPages);
    } catch (error) {
      console.error('Erro ao carregar balcões:', error);
      toast({
        title: "Erro ao carregar balcões",
        description: error instanceof BranchError ? error.message : "Erro desconhecido",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Função para gerar o próximo código automaticamente
  const generateNextCode = async () => {
    try {
      // Buscar todos os balcões para obter todos os códigos existentes
      const allBranchesResponse = await branchService.listBranches({
        page: 1,
        limit: 1000 // Buscar todos os balcões
      });

      const existingCodes = allBranchesResponse.branches
        .map(branch => parseInt(branch.code))
        .filter(code => !isNaN(code))
        .sort((a, b) => a - b);

      let nextCode = 1;

      // Procurar por lacunas na sequência
      for (let i = 0; i < existingCodes.length; i++) {
        if (existingCodes[i] !== nextCode) {
          break; // Encontrou uma lacuna
        }
        nextCode++;
      }

      // Formatar código com 3 dígitos e zeros à esquerda
      const formattedCode = nextCode.toString().padStart(3, '0');

      setFormData(prev => ({
        ...prev,
        code: formattedCode
      }));

    } catch (error) {
      console.error('Erro ao gerar código:', error);
      // Em caso de erro, usar um código padrão
      setFormData(prev => ({
        ...prev,
        code: '001'
      }));
    }
  };

  // Validação do formulário
  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!formData.code.trim()) {
      newErrors.code = 'Código é obrigatório';
    } else if (formData.code.length < 2) {
      newErrors.code = 'Código deve ter pelo menos 2 caracteres';
    }

    if (!formData.name.trim()) {
      newErrors.name = 'Nome é obrigatório';
    } else if (formData.name.length < 2) {
      newErrors.name = 'Nome deve ter pelo menos 2 caracteres';
    }

    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Email deve ter um formato válido';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Função para criar/atualizar balcão
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      toast({
        title: "Erro de validação",
        description: "Por favor, corrija os erros no formulário",
        variant: "destructive"
      });
      return;
    }

    setIsSubmitting(true);

    try {
      if (editingBranch) {
        // Atualizar balcão existente
        await branchService.updateBranch(editingBranch.id, formData);
        toast({
          title: "Balcão atualizado",
          description: `Balcão ${formData.name} foi atualizado com sucesso`,
        });
      } else {
        // Criar novo balcão
        await branchService.createBranch(formData);
        toast({
          title: "Balcão criado",
          description: `Balcão ${formData.name} foi criado com sucesso`,
        });
      }

      // Limpar formulário e recarregar lista
      resetForm();
      await loadBranches();
    } catch (error) {
      console.error('Erro ao salvar balcão:', error);
      toast({
        title: editingBranch ? "Erro ao atualizar balcão" : "Erro ao criar balcão",
        description: error instanceof BranchError ? error.message : "Erro desconhecido",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Função para editar balcão
  const handleEditBranch = (branch: Branch) => {
    setEditingBranch(branch);
    setFormData({
      code: branch.code,
      name: branch.name,
      address: branch.address || '',
      phone: branch.phone || '',
      email: branch.email || ''
    });
    setErrors({});
  };

  // Função para deletar balcão
  const handleDeleteBranch = async (branch: Branch) => {
    if (!confirm(`Tem certeza que deseja remover o balcão "${branch.name}"?`)) {
      return;
    }

    try {
      await branchService.deleteBranch(branch.id);
      toast({
        title: "Balcão removido",
        description: `Balcão ${branch.name} foi removido com sucesso`,
      });
      await loadBranches();
    } catch (error) {
      console.error('Erro ao remover balcão:', error);
      toast({
        title: "Erro ao remover balcão",
        description: error instanceof BranchError ? error.message : "Erro desconhecido",
        variant: "destructive"
      });
    }
  };

  // Função para alternar status do balcão
  const handleToggleStatus = async (branch: Branch) => {
    try {
      await branchService.toggleBranchStatus(branch.id, !branch.is_active);
      toast({
        title: "Status alterado",
        description: `Balcão ${branch.name} foi ${!branch.is_active ? 'ativado' : 'desativado'}`,
      });
      await loadBranches();
    } catch (error) {
      console.error('Erro ao alterar status:', error);
      toast({
        title: "Erro ao alterar status",
        description: error instanceof BranchError ? error.message : "Erro desconhecido",
        variant: "destructive"
      });
    }
  };

  // Função para resetar formulário
  const resetForm = () => {
    setFormData({
      code: '',
      name: '',
      address: '',
      phone: '',
      email: ''
    });
    setEditingBranch(null);
    setErrors({});

    // Gerar novo código após resetar o formulário
    setTimeout(() => {
      if (branches.length > 0) {
        generateNextCode();
      }
    }, 100);
  };

  // Função para obter badge de status
  const getStatusBadge = (isActive: boolean) => {
    return (
      <Badge variant={isActive ? "default" : "secondary"}>
        {isActive ? "Ativo" : "Inativo"}
      </Badge>
    );
  };

  return (
    <div className="space-y-6 content-container">
      <div>
        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Gestão de Balcões</h1>
        <p className="text-gray-600 dark:text-gray-400">Registar e gerir balcões do banco</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Lista de Balcões */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle className="text-lg font-semibold flex items-center">
                  Lista de Balcões
                  <Badge variant="secondary" className="ml-2">
                    {totalBranches}
                  </Badge>
                </CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              {/* Controles da tabela */}
              <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-4">
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Mostrar</span>
                  <Select
                    value={itemsPerPage.toString()}
                    onValueChange={(value) => {
                      setItemsPerPage(parseInt(value));
                      setCurrentPage(1);
                    }}
                  >
                    <SelectTrigger className="w-20">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="5">5</SelectItem>
                      <SelectItem value="10">10</SelectItem>
                      <SelectItem value="25">25</SelectItem>
                      <SelectItem value="50">50</SelectItem>
                    </SelectContent>
                  </Select>
                  <span className="text-sm text-gray-600 dark:text-gray-400">entradas</span>
                </div>

                <div className="flex items-center space-x-2">
                  <Search className="h-4 w-4 text-gray-400" />
                  <Input
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    placeholder="Buscar balcões..."
                    className="w-64"
                  />
                </div>
              </div>

              {/* Loading State */}
              {isLoading && (
                <div className="flex justify-center items-center py-8">
                  <Loader2 className="h-6 w-6 animate-spin" />
                  <span className="ml-2">Carregando balcões...</span>
                </div>
              )}

              {/* Tabela */}
              {!isLoading && (
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Código</TableHead>
                        <TableHead>Nome</TableHead>
                        <TableHead>Endereço</TableHead>
                        <TableHead>Telefone</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Ações</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {branches.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={6} className="text-center py-8 text-gray-500">
                            {searchTerm ? 'Nenhum balcão encontrado com os critérios de busca' : 'Nenhum balcão cadastrado'}
                          </TableCell>
                        </TableRow>
                      ) : (
                        branches.map((branch) => (
                          <TableRow key={branch.id}>
                            <TableCell className="font-medium">{branch.code}</TableCell>
                            <TableCell>{branch.name}</TableCell>
                            <TableCell className="max-w-xs truncate">
                              {branch.address || '-'}
                            </TableCell>
                            <TableCell>{branch.phone || '-'}</TableCell>
                            <TableCell>{getStatusBadge(branch.is_active)}</TableCell>
                            <TableCell>
                              <ActionMenu
                                items={[
                                  {
                                    label: 'Editar',
                                    icon: Pencil,
                                    onClick: () => handleEditBranch(branch)
                                  },
                                  {
                                    label: branch.is_active ? 'Desativar' : 'Ativar',
                                    icon: branch.is_active ? EyeOff : Eye,
                                    onClick: () => handleToggleStatus(branch),
                                    variant: branch.is_active ? 'warning' : 'default',
                                    separator: true
                                  },
                                  {
                                    label: 'Excluir',
                                    icon: Trash2,
                                    onClick: () => handleDeleteBranch(branch),
                                    variant: 'destructive'
                                  }
                                ]}
                              />
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>
              )}

              {/* Paginação */}
              {!isLoading && totalPages > 1 && (
                <div className="flex items-center justify-between mt-4">
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    Mostrando {((currentPage - 1) * itemsPerPage) + 1} a {Math.min(currentPage * itemsPerPage, totalBranches)} de {totalBranches} entradas
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                      disabled={currentPage === 1}
                    >
                      Anterior
                    </Button>
                    <span className="text-sm">
                      Página {currentPage} de {totalPages}
                    </span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                      disabled={currentPage === totalPages}
                    >
                      Próxima
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Formulário de Balcão */}
        <div>
          <Card>
            <CardHeader>
              <CardTitle className="text-lg font-semibold flex items-center">
                <Plus className="h-5 w-5 mr-2" />
                {editingBranch ? 'Editar Balcão' : 'Adicionar Balcão'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-4">
                {/* Código do Balcão */}
                <div className="space-y-2">
                  <Label htmlFor="code">
                    Código <span className="text-red-500">*</span>
                    {!editingBranch && (
                      <span className="text-xs text-gray-500 ml-2">(Gerado automaticamente)</span>
                    )}
                  </Label>
                  <Input
                    id="code"
                    value={formData.code}
                    onChange={(e) => setFormData(prev => ({ ...prev, code: e.target.value }))}
                    placeholder="Ex: 001"
                    className={errors.code ? 'border-red-500' : ''}
                    readOnly={!editingBranch}
                    disabled={!editingBranch}
                  />
                  {errors.code && (
                    <Alert variant="destructive" className="py-2">
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription className="text-sm">
                        {errors.code}
                      </AlertDescription>
                    </Alert>
                  )}
                  {!editingBranch && (
                    <p className="text-xs text-gray-500">
                      O código é gerado automaticamente baseado nos balcões existentes
                    </p>
                  )}
                </div>

                {/* Nome do Balcão */}
                <div className="space-y-2">
                  <Label htmlFor="name">
                    Nome <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="Digite o nome do balcão"
                    className={errors.name ? 'border-red-500' : ''}
                  />
                  {errors.name && (
                    <Alert variant="destructive" className="py-2">
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription className="text-sm">
                        {errors.name}
                      </AlertDescription>
                    </Alert>
                  )}
                </div>

                {/* Endereço */}
                <div className="space-y-2">
                  <Label htmlFor="address">Endereço</Label>
                  <Input
                    id="address"
                    value={formData.address}
                    onChange={(e) => setFormData(prev => ({ ...prev, address: e.target.value }))}
                    placeholder="Digite o endereço completo"
                    className={errors.address ? 'border-red-500' : ''}
                  />
                  {errors.address && (
                    <Alert variant="destructive" className="py-2">
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription className="text-sm">
                        {errors.address}
                      </AlertDescription>
                    </Alert>
                  )}
                </div>

                {/* Telefone */}
                <div className="space-y-2">
                  <Label htmlFor="phone">Telefone</Label>
                  <Input
                    id="phone"
                    value={formData.phone}
                    onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                    placeholder="Digite o telefone"
                    className={errors.phone ? 'border-red-500' : ''}
                  />
                  {errors.phone && (
                    <Alert variant="destructive" className="py-2">
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription className="text-sm">
                        {errors.phone}
                      </AlertDescription>
                    </Alert>
                  )}
                </div>

                {/* Email */}
                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    value={formData.email}
                    onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                    placeholder="Digite o email"
                    className={errors.email ? 'border-red-500' : ''}
                  />
                  {errors.email && (
                    <Alert variant="destructive" className="py-2">
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription className="text-sm">
                        {errors.email}
                      </AlertDescription>
                    </Alert>
                  )}
                </div>

                {/* Botões */}
                <div className="flex gap-2 pt-4">
                  <Button
                    type="submit"
                    disabled={isSubmitting}
                    className="flex-1"
                  >
                    {isSubmitting ? (
                      <>
                        <Loader2 className="h-4 w-4 animate-spin mr-2" />
                        {editingBranch ? 'Atualizando...' : 'Criando...'}
                      </>
                    ) : (
                      <>
                        <CheckCircle className="h-4 w-4 mr-2" />
                        {editingBranch ? 'Atualizar' : 'Criar Balcão'}
                      </>
                    )}
                  </Button>

                  {editingBranch && (
                    <Button
                      type="button"
                      variant="outline"
                      onClick={resetForm}
                      disabled={isSubmitting}
                    >
                      Cancelar
                    </Button>
                  )}
                </div>
              </form>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default RegistarBalcao;
