// Token Management Utility
// Utilitário para gestão de tokens JWT

import authService from '@/services/authService';

// Chaves para localStorage
const TOKEN_KEY = 'twins-bank-token';
const REFRESH_TOKEN_KEY = 'twins-bank-refresh-token';
const SESSION_ID_KEY = 'twins-bank-session-id';
const TOKEN_EXPIRY_KEY = 'twins-bank-token-expiry';

// Interface para dados do token
interface TokenData {
  accessToken: string;
  refreshToken: string;
  sessionId: string;
  expiresIn: string;
}

// Classe para gestão de tokens
export class TokenManager {
  private static instance: TokenManager;
  private refreshPromise: Promise<void> | null = null;

  private constructor() {}

  public static getInstance(): TokenManager {
    if (!TokenManager.instance) {
      TokenManager.instance = new TokenManager();
    }
    return TokenManager.instance;
  }

  /**
   * Armazenar dados do token
   */
  public storeTokenData(tokenData: TokenData): void {
    localStorage.setItem(TOKEN_KEY, tokenData.accessToken);
    localStorage.setItem(REFRESH_TOKEN_KEY, tokenData.refreshToken);
    localStorage.setItem(SESSION_ID_KEY, tokenData.sessionId);
    
    // Calcular e armazenar tempo de expiração
    const expiryTime = this.calculateExpiryTime(tokenData.expiresIn);
    localStorage.setItem(TOKEN_EXPIRY_KEY, expiryTime.toString());
  }

  /**
   * Obter token de acesso atual
   */
  public getAccessToken(): string | null {
    return localStorage.getItem(TOKEN_KEY);
  }

  /**
   * Obter token de refresh atual
   */
  public getRefreshToken(): string | null {
    return localStorage.getItem(REFRESH_TOKEN_KEY);
  }

  /**
   * Obter ID da sessão atual
   */
  public getSessionId(): string | null {
    return localStorage.getItem(SESSION_ID_KEY);
  }

  /**
   * Verificar se o token está expirado
   */
  public isTokenExpired(): boolean {
    const expiryTime = localStorage.getItem(TOKEN_EXPIRY_KEY);
    if (!expiryTime) return true;
    
    const now = Date.now();
    const expiry = parseInt(expiryTime, 10);
    
    // Considerar expirado se faltam menos de 5 minutos
    const bufferTime = 5 * 60 * 1000; // 5 minutos em ms
    return now >= (expiry - bufferTime);
  }

  /**
   * Verificar se há token válido
   */
  public hasValidToken(): boolean {
    const token = this.getAccessToken();
    return !!token && !this.isTokenExpired();
  }

  /**
   * Renovar token automaticamente
   */
  public async refreshTokenIfNeeded(): Promise<boolean> {
    // Se já há uma renovação em andamento, aguardar
    if (this.refreshPromise) {
      await this.refreshPromise;
      return this.hasValidToken();
    }

    // Se o token ainda é válido, não renovar
    if (this.hasValidToken()) {
      return true;
    }

    // Se não há refresh token, não é possível renovar
    if (!this.getRefreshToken()) {
      return false;
    }

    // Iniciar renovação
    this.refreshPromise = this.performTokenRefresh();
    
    try {
      await this.refreshPromise;
      return this.hasValidToken();
    } catch (error) {
      console.error('Erro ao renovar token:', error);
      return false;
    } finally {
      this.refreshPromise = null;
    }
  }

  /**
   * Executar renovação do token
   */
  private async performTokenRefresh(): Promise<void> {
    try {
      const response = await authService.refreshToken();
      
      // Atualizar dados do token
      const tokenData: TokenData = {
        accessToken: response.accessToken,
        refreshToken: this.getRefreshToken()!, // Manter o refresh token atual
        sessionId: response.sessionId,
        expiresIn: response.expiresIn
      };
      
      this.storeTokenData(tokenData);
      
    } catch (error) {
      // Se a renovação falhar, limpar todos os tokens
      this.clearTokens();
      throw error;
    }
  }

  /**
   * Limpar todos os tokens
   */
  public clearTokens(): void {
    localStorage.removeItem(TOKEN_KEY);
    localStorage.removeItem(REFRESH_TOKEN_KEY);
    localStorage.removeItem(SESSION_ID_KEY);
    localStorage.removeItem(TOKEN_EXPIRY_KEY);
  }

  /**
   * Calcular tempo de expiração baseado na string de duração
   */
  private calculateExpiryTime(expiresIn: string): number {
    const now = Date.now();
    
    // Converter string como "24h", "30m", "60s" para milissegundos
    const match = expiresIn.match(/^(\d+)([hms])$/);
    if (!match) {
      // Se não conseguir parsear, assumir 24 horas
      return now + (24 * 60 * 60 * 1000);
    }
    
    const value = parseInt(match[1], 10);
    const unit = match[2];
    
    let multiplier: number;
    switch (unit) {
      case 'h':
        multiplier = 60 * 60 * 1000; // horas para ms
        break;
      case 'm':
        multiplier = 60 * 1000; // minutos para ms
        break;
      case 's':
        multiplier = 1000; // segundos para ms
        break;
      default:
        multiplier = 60 * 60 * 1000; // padrão: horas
    }
    
    return now + (value * multiplier);
  }

  /**
   * Configurar interceptador para renovação automática
   */
  public setupAutoRefresh(): void {
    // Verificar tokens a cada 5 minutos
    setInterval(async () => {
      if (this.getAccessToken() && this.isTokenExpired()) {
        try {
          await this.refreshTokenIfNeeded();
        } catch (error) {
          console.warn('Renovação automática de token falhou:', error);
        }
      }
    }, 5 * 60 * 1000); // 5 minutos
  }
}

// Instância singleton
export const tokenManager = TokenManager.getInstance();

// Configurar renovação automática ao importar
tokenManager.setupAutoRefresh();

export default tokenManager;
