const jwt = require('jsonwebtoken');
const { AppError, catchAsync } = require('../core/errorHandler');
const { executeQuery } = require('../config/database');
const logger = require('../core/logger');

/**
 * Middleware de autenticação JWT
 */
const authenticate = catchAsync(async (req, res, next) => {
  // 1. Obter token do header
  let token;
  if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
    token = req.headers.authorization.split(' ')[1];
  }

  if (!token) {
    return next(new AppError('Token de acesso necessário', 401, 'NO_TOKEN'));
  }

  // 2. Verificar token
  let decoded;
  try {
    decoded = jwt.verify(token, process.env.JWT_SECRET);
  } catch (error) {
    if (error.name === 'TokenExpiredError') {
      return next(new AppError('Token expirado', 401, 'EXPIRED_TOKEN'));
    }
    return next(new AppError('Token inválido', 401, 'INVALID_TOKEN'));
  }

  // 3. Verificar se o utilizador ainda existe
  const user = await executeQuery(
    `SELECT u.*, r.name as role_name 
     FROM users u 
     JOIN roles r ON u.role_id = r.id 
     WHERE u.id = ? AND u.is_active = true`,
    [decoded.id]
  );

  if (!user || user.length === 0) {
    return next(new AppError('Utilizador não encontrado ou inativo', 401, 'USER_NOT_FOUND'));
  }

  // 4. Verificar se a sessão ainda é válida
  const session = await executeQuery(
    'SELECT * FROM user_sessions WHERE user_id = ? AND expires_at > NOW()',
    [decoded.id]
  );

  if (!session || session.length === 0) {
    return next(new AppError('Sessão expirada', 401, 'SESSION_EXPIRED'));
  }

  // 5. Adicionar utilizador ao request
  req.user = {
    id: user[0].id,
    full_name: user[0].full_name,
    email: user[0].email,
    role_id: user[0].role_id,
    role_name: user[0].role_name,
    branch_id: user[0].branch_id,
    is_active: user[0].is_active
  };

  logger.auth(`Utilizador autenticado: ${user[0].email}`, { userId: user[0].id });
  next();
});

/**
 * Middleware de autorização por roles
 */
const authorize = (...roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return next(new AppError('Utilizador não autenticado', 401, 'NOT_AUTHENTICATED'));
    }

    if (!roles.includes(req.user.role_name)) {
      logger.security(`Tentativa de acesso negado para ${req.user.email}`, {
        userId: req.user.id,
        requiredRoles: roles,
        userRole: req.user.role_name,
        path: req.path
      });
      
      return next(new AppError('Não tem permissão para aceder a este recurso', 403, 'INSUFFICIENT_PERMISSIONS'));
    }

    next();
  };
};

/**
 * Middleware de autorização por módulo e ação
 */
const authorizeModule = (module, action = 'read') => {
  return catchAsync(async (req, res, next) => {
    if (!req.user) {
      return next(new AppError('Utilizador não autenticado', 401, 'NOT_AUTHENTICATED'));
    }

    // Verificar permissões do utilizador para o módulo
    const permissions = await executeQuery(
      `SELECT rp.* FROM role_permissions rp 
       JOIN roles r ON rp.role_id = r.id 
       WHERE r.id = ? AND rp.module = ? AND rp.actions LIKE ?`,
      [req.user.role_id, module, `%${action}%`]
    );

    if (!permissions || permissions.length === 0) {
      logger.security(`Acesso negado ao módulo ${module}:${action} para ${req.user.email}`, {
        userId: req.user.id,
        module,
        action,
        userRole: req.user.role_name
      });
      
      return next(new AppError(`Não tem permissão para ${action} no módulo ${module}`, 403, 'MODULE_ACCESS_DENIED'));
    }

    next();
  });
};

/**
 * Middleware para verificar se o utilizador é proprietário do recurso
 */
const authorizeOwnership = (resourceField = 'user_id') => {
  return (req, res, next) => {
    if (!req.user) {
      return next(new AppError('Utilizador não autenticado', 401, 'NOT_AUTHENTICATED'));
    }

    // Admin e Gerente podem aceder a todos os recursos
    if (['admin', 'gerente'].includes(req.user.role_name)) {
      return next();
    }

    // Verificar se o utilizador é proprietário do recurso
    const resourceUserId = req.params.userId || req.body[resourceField] || req.query[resourceField];
    
    if (resourceUserId && resourceUserId !== req.user.id) {
      logger.security(`Tentativa de acesso a recurso de outro utilizador`, {
        userId: req.user.id,
        targetUserId: resourceUserId,
        path: req.path
      });
      
      return next(new AppError('Não pode aceder a recursos de outros utilizadores', 403, 'OWNERSHIP_VIOLATION'));
    }

    next();
  };
};

/**
 * Middleware opcional de autenticação (não falha se não houver token)
 */
const optionalAuth = catchAsync(async (req, res, next) => {
  let token;
  if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
    token = req.headers.authorization.split(' ')[1];
  }

  if (!token) {
    return next();
  }

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    const user = await executeQuery(
      `SELECT u.*, r.name as role_name 
       FROM users u 
       JOIN roles r ON u.role_id = r.id 
       WHERE u.id = ? AND u.is_active = true`,
      [decoded.id]
    );

    if (user && user.length > 0) {
      req.user = {
        id: user[0].id,
        full_name: user[0].full_name,
        email: user[0].email,
        role_id: user[0].role_id,
        role_name: user[0].role_name,
        branch_id: user[0].branch_id,
        is_active: user[0].is_active
      };
    }
  } catch (error) {
    // Ignorar erros de token em autenticação opcional
  }

  next();
});

module.exports = {
  authenticate,
  authorize,
  authorizeModule,
  authorizeOwnership,
  optionalAuth
};
