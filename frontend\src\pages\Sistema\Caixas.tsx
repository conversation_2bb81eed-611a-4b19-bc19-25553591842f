import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { Wallet, Search, Trash2 } from 'lucide-react';

interface Caixa {
  id: number;
  numeroCaixa: number;
  status: 'Activo' | 'Inactivo';
  saldo: number;
  operador: string;
}

const Caixas = () => {
  const [caixas] = useState<Caixa[]>([
    {
      id: 1,
      numeroCaixa: 1,
      status: 'Activo',
      saldo: 5000000,
      operador: 'Estêv<PERSON> <PERSON>'
    },
    {
      id: 2,
      numeroCaixa: 2,
      status: 'Activo',
      saldo: 4000000,
      operador: 'Estê<PERSON><PERSON>'
    },
    {
      id: 3,
      numeroCaixa: 3,
      status: 'Activo',
      saldo: 7650000,
      operador: 'Estêvão Manuel Gomes Mfulama'
    },
    {
      id: 4,
      numeroCaixa: 4,
      status: 'Activo',
      saldo: 4000000,
      operador: 'Estêvão Manuel Gomes Mfulama'
    },
    {
      id: 5,
      numeroCaixa: 5,
      status: 'Activo',
      saldo: 1000000,
      operador: 'Estêvão Manuel Gomes Mfulama'
    },
    {
      id: 6,
      numeroCaixa: 6,
      status: 'Activo',
      saldo: 89999999850998,
      operador: 'Estêvão Manuel Gomes Mfulama'
    }
  ]);

  const [filtros, setFiltros] = useState({
    search: '',
    itemsPorPagina: '1'
  });

  const { toast } = useToast();

  const formatarSaldo = (valor: number) => {
    return new Intl.NumberFormat('pt-AO').format(valor);
  };

  const caixasFiltradas = caixas.filter(caixa => {
    const matchSearch = !filtros.search ||
      caixa.numeroCaixa.toString().includes(filtros.search) ||
      caixa.operador.toLowerCase().includes(filtros.search.toLowerCase()) ||
      caixa.status.toLowerCase().includes(filtros.search.toLowerCase());

    return matchSearch;
  });

  const handleExcluirCaixa = (id: number) => {
    toast({
      title: "Excluir caixa",
      description: "Funcionalidade de exclusão será implementada",
      variant: "destructive"
    });
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'Activo':
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Activo</Badge>;
      case 'Inactivo':
        return <Badge className="bg-red-100 text-red-800 hover:bg-red-100">Inactivo</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  return (
    <div className="space-y-6 content-container">
      <div>
        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 flex items-center gap-2">
          <Wallet className="h-8 w-8" />
          Lista Caixas
          <Badge className="bg-red-500 text-white hover:bg-red-500 ml-2">
            {caixas.length}
          </Badge>
        </h1>
        <p className="text-gray-600 dark:text-gray-400 mt-2">Gestão de caixas do sistema bancário</p>
      </div>

      {/* Enhanced Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="border-green-200 hover:shadow-lg transition-shadow duration-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-green-600">Caixas Activos</CardTitle>
            <Wallet className="h-5 w-5 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-green-600">
              {caixas.filter(c => c.status === 'Activo').length}
            </div>
            <p className="text-xs text-green-500 mt-1">
              Em operação
            </p>
          </CardContent>
        </Card>

        <Card className="border-red-200 hover:shadow-lg transition-shadow duration-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-red-600">Caixas Inactivos</CardTitle>
            <Wallet className="h-5 w-5 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-red-600">
              {caixas.filter(c => c.status === 'Inactivo').length}
            </div>
            <p className="text-xs text-red-500 mt-1">
              Fora de operação
            </p>
          </CardContent>
        </Card>

        <Card className="border-blue-200 hover:shadow-lg transition-shadow duration-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-blue-600">Saldo Total</CardTitle>
            <Wallet className="h-5 w-5 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-blue-600">
              {formatarSaldo(caixas.reduce((total, caixa) => total + caixa.saldo, 0))} Kz
            </div>
            <p className="text-xs text-blue-500 mt-1">
              Todos os caixas
            </p>
          </CardContent>
        </Card>

        <Card className="border-purple-200 hover:shadow-lg transition-shadow duration-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-purple-600">Saldo Médio</CardTitle>
            <Wallet className="h-5 w-5 text-purple-500" />
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-purple-600">
              {formatarSaldo(Math.round(caixas.reduce((total, caixa) => total + caixa.saldo, 0) / caixas.length))} Kz
            </div>
            <p className="text-xs text-purple-500 mt-1">
              Por caixa
            </p>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardContent className="p-6">
          {/* Controles da tabela */}
          <div className="flex justify-between items-center mb-4">
            <div className="flex items-center space-x-2">
              <span className="text-sm">Show</span>
              <Select value={filtros.itemsPorPagina} onValueChange={(value) => setFiltros(prev => ({ ...prev, itemsPorPagina: value }))}>
                <SelectTrigger className="w-16">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1">1</SelectItem>
                  <SelectItem value="5">5</SelectItem>
                  <SelectItem value="10">10</SelectItem>
                  <SelectItem value="25">25</SelectItem>
                  <SelectItem value="50">50</SelectItem>
                </SelectContent>
              </Select>
              <span className="text-sm">entries</span>
            </div>

            <div className="flex items-center space-x-2">
              <span className="text-sm">Search:</span>
              <div className="relative">
                <Input
                  placeholder=""
                  value={filtros.search}
                  onChange={(e) => setFiltros(prev => ({ ...prev, search: e.target.value }))}
                  className="w-48"
                />
              </div>
            </div>
          </div>

          {/* Tabela */}
          <div className="table-container rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="cursor-pointer hover:bg-gray-50 min-w-[80px]">
                    Nº Caixa ↕
                  </TableHead>
                  <TableHead className="cursor-pointer hover:bg-gray-50 min-w-[100px]">
                    Status ↕
                  </TableHead>
                  <TableHead className="cursor-pointer hover:bg-gray-50 min-w-[120px]">
                    Saldo ↕
                  </TableHead>
                  <TableHead className="cursor-pointer hover:bg-gray-50 min-w-[200px]">
                    Operador ↕
                  </TableHead>
                  <TableHead className="cursor-pointer hover:bg-gray-50 min-w-[80px]">
                    Action ↕
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {caixasFiltradas.slice(0, parseInt(filtros.itemsPorPagina)).map((caixa) => (
                  <TableRow key={caixa.id}>
                    <TableCell className="font-medium">{caixa.numeroCaixa}</TableCell>
                    <TableCell>{getStatusBadge(caixa.status)}</TableCell>
                    <TableCell className="font-medium">{formatarSaldo(caixa.saldo)}</TableCell>
                    <TableCell className="table-cell-content" title={caixa.operador}>
                      {caixa.operador}
                    </TableCell>
                    <TableCell>
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={() => handleExcluirCaixa(caixa.id)}
                        className="h-8 w-8 p-0"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {/* Informações da paginação */}
          <div className="flex justify-between items-center mt-4 text-sm text-gray-600">
            <div>
              Showing {Math.min(parseInt(filtros.itemsPorPagina), caixasFiltradas.length)} of {caixasFiltradas.length} entries
              {filtros.search && ` (filtered from ${caixas.length} total entries)`}
            </div>
            
            {caixasFiltradas.length > parseInt(filtros.itemsPorPagina) && (
              <div className="flex items-center space-x-2">
                <Button variant="outline" size="sm" disabled>
                  Previous
                </Button>
                <Button variant="outline" size="sm" className="bg-primary text-white">
                  1
                </Button>
                <Button variant="outline" size="sm">
                  Next
                </Button>
              </div>
            )}
          </div>
        </CardContent>
      </Card>


    </div>
  );
};

export default Caixas;
