# Twins_Bank Monorepo .gitignore

# Dependencies
node_modules/
frontend/node_modules/
backend/node_modules/

# Production builds
frontend/dist/
frontend/build/
backend/dist/
backend/build/
dist/
dist-ssr/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
frontend/.env.local
backend/.env.local
*.local

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*
backend/logs/

# Coverage and test results
coverage/
*.lcov
test-results/
playwright-report/

# TypeScript cache
*.tsbuildinfo

# Cache directories
.cache/
.parcel-cache/
.eslintcache
.npm/

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea/
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
*.swp
*.swo
.augment

# Database files
*.sqlite
*.sqlite3
*.db

# Temporary files
tmp/
temp/
*.tmp
*.temp
*.bak
